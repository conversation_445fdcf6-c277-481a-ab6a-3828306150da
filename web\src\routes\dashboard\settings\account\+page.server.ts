import { redirect, fail } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import type { PageServerLoad, Actions } from './$types.js';

// Define the schema with Zod for validation
const accountSchema = z.object({
  // Personal Information
  name: z.string().min(1, 'Name is required').max(100, 'Name is too long'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  bio: z.string().max(500, 'Bio should be less than 500 characters').optional(),
  profilePicture: z.string().optional().nullable(),

  // UI Preferences
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  viewMode: z.enum(['list', 'grid', 'compact']).default('list'),

  // Accessibility
  highContrast: z.boolean().default(false),
  reducedMotion: z.boolean().default(false),
  largeText: z.boolean().default(false),
  screenReader: z.boolean().default(false),

  // Privacy
  profileVisibility: z.enum(['public', 'private']).default('public'),
  allowDataCollection: z.boolean().default(true),
  allowThirdPartySharing: z.boolean().default(false),

  // Application Preferences
  autoParseResumes: z.boolean().default(true),
  autoSaveApplications: z.boolean().default(true),
  applicationReminders: z.boolean().default(true),

  // Job Search Preferences
  defaultRemotePreference: z.enum(['remote', 'hybrid', 'onsite', 'flexible']).default('hybrid'),
  showSalaryInListings: z.boolean().default(true),
  autoApplyEnabled: z.boolean().default(false),

  // Resume Preferences
  defaultResumeParsingEnabled: z.boolean().default(true),
  autoUpdateProfileFromResume: z.boolean().default(true),
  resumePrivacyLevel: z.enum(['public', 'private']).default('private'),

  // Cookie Preferences
  cookiePreferences: z
    .object({
      functional: z.boolean().default(true),
      analytics: z.boolean().default(true),
      advertising: z.boolean().default(false),
    })
    .optional(),
});

export const load: PageServerLoad = async ({ locals }) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get user data with complete details
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
  });

  if (!userData) {
    throw redirect(302, '/auth/sign-in');
  }

  // Extract account preferences from user data
  const preferences = (userData.preferences as any) || {};
  const accountPrefs = preferences.account || {};

  const userPreferences = {
    // Personal Information
    name: userData.name || '',
    email: userData.email,
    phone: accountPrefs.phone,
    bio: accountPrefs.bio,
    profilePicture: userData.image,

    // UI Preferences
    theme: accountPrefs.ui?.theme || accountPrefs.accessibility?.theme,
    viewMode: accountPrefs.ui?.viewMode || accountPrefs.accessibility?.viewMode,

    // Accessibility
    highContrast: accountPrefs.accessibility?.highContrast,
    reducedMotion: accountPrefs.accessibility?.reducedMotion,
    largeText: accountPrefs.accessibility?.largeText,
    screenReader: accountPrefs.accessibility?.screenReader,

    // Privacy Settings
    profileVisibility: accountPrefs.privacy?.profileVisibility,
    allowDataCollection: accountPrefs.privacy?.allowDataCollection,
    allowThirdPartySharing: accountPrefs.privacy?.allowThirdPartySharing,

    // Application Preferences
    autoParseResumes: accountPrefs.application?.autoParseResumes,
    autoSaveApplications: accountPrefs.application?.autoSaveApplications,
    applicationReminders: accountPrefs.application?.applicationReminders,

    // Job Search Preferences
    defaultRemotePreference: accountPrefs.jobSearch?.defaultRemotePreference,
    showSalaryInListings: accountPrefs.jobSearch?.showSalaryInListings,
    autoApplyEnabled: accountPrefs.jobSearch?.autoApplyEnabled,

    // Resume Preferences
    defaultResumeParsingEnabled: accountPrefs.resume?.defaultResumeParsingEnabled,
    autoUpdateProfileFromResume: accountPrefs.resume?.autoUpdateProfileFromResume,
    resumePrivacyLevel: accountPrefs.resume?.resumePrivacyLevel,

    // Cookie Preferences
    cookiePreferences: accountPrefs.cookiePreferences,
  };

  // Create the form with initial values
  const form = await superValidate(userPreferences, zod(accountSchema));

  return {
    user: userData,
    form,
  };
};

export const actions: Actions = {
  default: async ({ request, locals }) => {
    // Check if user is authenticated
    if (!locals.user) {
      throw redirect(302, '/auth/sign-in');
    }

    // Get user data
    const userData = await prisma.user.findUnique({
      where: { id: locals.user.id },
    });

    if (!userData) {
      throw redirect(302, '/auth/sign-in');
    }

    // Validate form data
    const form = await superValidate(request, zod(accountSchema));

    if (!form.valid) {
      return fail(400, { form });
    }

    try {
      // Get existing preferences or create an empty object
      const preferences = (userData.preferences as any) || {};

      // Update account preferences
      const updatedPreferences = {
        ...preferences,
        account: {
          ...(preferences.account || {}),
          phone: form.data.phone,
          bio: form.data.bio,
          ui: {
            theme: form.data.theme,
            viewMode: form.data.viewMode,
          },
          accessibility: {
            highContrast: form.data.highContrast,
            reducedMotion: form.data.reducedMotion,
            largeText: form.data.largeText,
            screenReader: form.data.screenReader,
          },
          privacy: {
            profileVisibility: form.data.profileVisibility,
            allowDataCollection: form.data.allowDataCollection,
            allowThirdPartySharing: form.data.allowThirdPartySharing,
          },
          application: {
            autoParseResumes: form.data.autoParseResumes,
            autoSaveApplications: form.data.autoSaveApplications,
            applicationReminders: form.data.applicationReminders,
          },
          jobSearch: {
            defaultRemotePreference: form.data.defaultRemotePreference,
            showSalaryInListings: form.data.showSalaryInListings,
            autoApplyEnabled: form.data.autoApplyEnabled,
          },
          resume: {
            defaultResumeParsingEnabled: form.data.defaultResumeParsingEnabled,
            autoUpdateProfileFromResume: form.data.autoUpdateProfileFromResume,
            resumePrivacyLevel: form.data.resumePrivacyLevel,
          },
          cookiePreferences: form.data.cookiePreferences || {
            functional: true,
            analytics: true,
            advertising: false,
          },
        },
      };

      // Update the user's name and preferences in the database
      await prisma.user.update({
        where: { id: userData.id },
        data: {
          name: form.data.name,
          image: form.data.profilePicture,
          preferences: updatedPreferences,
        },
      });

      // Return the updated user data to force a refresh of the user data in the layout
      return {
        form,
        success: true,
        user: {
          ...userData,
          name: form.data.name,
          image: form.data.profilePicture,
          preferences: updatedPreferences,
        },
      };
    } catch (error) {
      console.error('Error updating account settings:', error);
      return fail(500, { form, error: 'Failed to update account settings' });
    }
  },
};
