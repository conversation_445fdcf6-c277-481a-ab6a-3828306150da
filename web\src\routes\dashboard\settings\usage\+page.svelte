<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import {
    RefreshCw,
    Loader2,
    AlertTriangle,
    CheckCircle,
    Calendar,
    ChevronDown,
    TrendingUp,
    TrendingDown,
    Minus,
  } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import UsageHistoryChart from '$components/usage/UsageHistoryChart.svelte';
  import UsageOverview from '$components/usage/UsageOverview.svelte';
  import UsageDistributionChart from '$components/usage/UsageDistributionChart.svelte';
  import { FeatureCategory } from '$lib/models/features/features';
  import { goto } from '$app/navigation';

  // Get data from props with proper typing
  const props = $props<{
    data: {
      user: {
        id: string;
        email: string;
        name: string;
        role: string;
        image?: string;
      };
      featureUsage: any[];
      usageTrends?: {
        currentMonthTotal: number;
        previousMonthTotal: number;
        trendPercent: number;
        trendDirection: 'up' | 'down' | 'stable';
      };
    };
  }>();

  // Create reactive variables for the data with safe defaults
  let user = $state(props?.data?.user || {});

  // Initialize with feature usage data from server if available
  let initialFeatureData = $state(props?.data?.featureUsage || []);

  // Initialize usage trends data
  let usageTrends = $state(
    props?.data?.usageTrends || {
      currentMonthTotal: 0,
      previousMonthTotal: 0,
      trendPercent: 0,
      trendDirection: 'stable',
    }
  );

  // State
  let usageData = $state<any[]>([]);
  let featuresData = $state<any[]>(initialFeatureData);
  let categorizedFeatures = $state<Record<string, any[]>>({});
  let usageSummary = $state<any>(null);
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let selectedFeatureId = $state('');
  let selectedLimitId = $state('');
  let timeRange = $state('30d'); // Default to 30 days

  // Derived state
  let selectedFeature = $derived(featuresData.find((f) => f.id === selectedFeatureId));

  // Export usage data as CSV
  function exportUsageData() {
    try {
      const csvData = featuresData
        .map((feature) => {
          const usage = feature.usage || [];
          return usage.map((u: any) => ({
            Feature: feature.name,
            Category: feature.category,
            Limit: u.limitName,
            Used: u.used,
            Limit_Value: u.limit,
            Remaining: u.remaining,
            Percentage: u.percentUsed,
            Period: u.period,
          }));
        })
        .flat();

      if (csvData.length === 0) {
        alert('No usage data to export');
        return;
      }

      const headers = Object.keys(csvData[0]);
      const csvContent = [
        headers.join(','),
        ...csvData.map((row) => headers.map((header) => `"${row[header] || ''}"`).join(',')),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `usage-data-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data');
    }
  }

  // Load usage data on component initialization
  loadUsageData();

  // Load usage data
  async function loadUsageData() {
    isLoading = true;
    error = null;

    try {
      // Make sure we have a valid user ID before making API calls
      if (!user.id) {
        throw new Error('User ID is required to fetch usage data');
      }

      // Use the feature usage data from the server
      if (props?.data?.featureUsage) {
        featuresData = props.data.featureUsage;
        console.log('Using server-side feature usage data:', featuresData);

        // Calculate usage summary from the feature data
        const totalFeatures = featuresData.length;
        const featuresUsed = featuresData.filter(
          (f) => f.usage && f.usage.length > 0 && f.usage.some((u) => u.used > 0 && !u.placeholder)
        ).length;
        const featuresWithLimits = featuresData.filter(
          (f) => f.usage && f.usage.length > 0 && f.usage.some((u) => u.limit !== 'unlimited')
        ).length;
        const featuresAtLimit = featuresData.filter(
          (f) =>
            f.usage &&
            f.usage.length > 0 &&
            f.usage.some(
              (u) =>
                !u.placeholder &&
                u.limit !== 'unlimited' &&
                typeof u.limit === 'number' &&
                u.used >= u.limit
            )
        ).length;

        // Get top features by usage percentage
        const topFeatures = featuresData
          .filter(
            (f) =>
              f.usage && f.usage.length > 0 && f.usage.some((u) => !u.placeholder && u.used > 0)
          )
          .map((f) => {
            // Find the usage with the highest percentage
            const highestUsage =
              f.usage
                .filter((u) => !u.placeholder)
                .reduce((highest, current) => {
                  if (current.percentUsed === undefined) return highest;
                  if (!highest || highest.percentUsed === undefined) return current;
                  return current.percentUsed > highest.percentUsed ? current : highest;
                }, null) || f.usage[0];

            return {
              featureId: f.id,
              featureName: f.name,
              used: highestUsage.used,
              limit: highestUsage.limit,
              percentUsed: highestUsage.percentUsed,
            };
          })
          .filter((f) => f.percentUsed !== undefined)
          .sort((a, b) => (b.percentUsed || 0) - (a.percentUsed || 0))
          .slice(0, 5);

        usageSummary = {
          totalFeatures,
          featuresUsed,
          featuresWithLimits,
          featuresAtLimit,
          topFeatures,
        };

        console.log('Calculated usage summary:', usageSummary);
      } else {
        console.warn('No feature usage data available from server');
        featuresData = [];
        usageSummary = {
          totalFeatures: 0,
          featuresUsed: 0,
          featuresWithLimits: 0,
          featuresAtLimit: 0,
          topFeatures: [],
        };
      }

      // Organize features by category
      categorizedFeatures = featuresData.reduce((acc, feature) => {
        const category = feature.category;
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(feature);
        return acc;
      }, {});

      // Format usage data for compatibility with existing components
      usageData = featuresData.flatMap(
        (feature) =>
          feature.usage?.map((usage: any) => ({
            id: `${feature.id}-${usage.limitId}`,
            featureId: feature.id,
            featureName: feature.name,
            limitId: usage.limitId,
            limitName: usage.limitName,
            used: usage.used,
            limit: usage.limit,
            remaining: usage.remaining,
            percentUsed: usage.percentUsed,
            period: usage.period,
          })) || []
      );

      // If no feature is selected, select the first one with usage
      if (!selectedFeatureId && featuresData.length > 0) {
        // Find a feature with usage
        const featureWithUsage = featuresData.find((f) => f.usage && f.usage.length > 0);
        if (featureWithUsage) {
          selectedFeatureId = featureWithUsage.id;
          if (featureWithUsage.usage && featureWithUsage.usage.length > 0) {
            selectedLimitId = featureWithUsage.usage[0].limitId;
          }
        } else if (featuresData.length > 0) {
          // If no feature has usage, select the first feature
          selectedFeatureId = featuresData[0].id;
        }
      }

      // If we have no data or no features with usage data, but no error, set a friendly message
      if (!error) {
        if (featuresData.length === 0) {
          error =
            "No feature usage data found. This could be because you haven't used any features yet, or because the feature usage tracking system is still being set up.";
        } else if (featuresData.every((f) => !f.usage || f.usage.length === 0)) {
          error =
            'Features are available but no usage data has been recorded yet. Start using the application to see your usage statistics.';
        } else if (
          featuresData.every((f) => f.usage && f.usage.every((u) => u.placeholder === true))
        ) {
          // This is not an error, just a notice
          console.info(
            'All features have placeholder usage data. No actual usage has been recorded yet.'
          );
        }
      }
    } catch (err) {
      console.error('Error loading feature usage:', err);
      error = err.message || 'Failed to load feature usage';

      // Reset data on error
      featuresData = [];
      usageSummary = null;
      categorizedFeatures = {};
      usageData = [];
    } finally {
      isLoading = false;
    }
  }

  // Get features with usage for display
  let featuresWithUsage = $state([]);

  // Update featuresWithUsage when featuresData changes
  $effect(() => {
    featuresWithUsage = featuresData
      .filter((feature) => feature.usage && feature.usage.length > 0)
      .sort((a, b) => a.name.localeCompare(b.name));
  });

  // No need for limitsForFeature as we're using selectedFeature.usage directly

  // Get features by category for display
  let featuresByCategory = $derived(() => {
    const result: Record<string, any> = {};

    // Create a map of category names
    const categoryNames: Record<string, string> = {
      [FeatureCategory.Core]: 'Core Features',
      [FeatureCategory.JobSearch]: 'Job Search',
      [FeatureCategory.Resume]: 'Resume',
      [FeatureCategory.Applications]: 'Applications',
      [FeatureCategory.Analytics]: 'Analytics',
      [FeatureCategory.Team]: 'Team',
      [FeatureCategory.Integration]: 'Integration',
    };

    // Group features by category
    Object.entries(categorizedFeatures).forEach(([category, features]) => {
      // Only include categories with features that have usage
      const featuresWithUsage = features.filter((f) => f.usage && f.usage.length > 0);
      if (featuresWithUsage.length > 0) {
        result[category] = {
          name: categoryNames[category] || category,
          features: featuresWithUsage.sort((a, b) => a.name.localeCompare(b.name)),
        };
      }
    });

    return result;
  });
</script>

<SEO
  title="Feature Usage | Hirli"
  description="Track your feature usage and subscription limits"
  keywords="feature usage, subscription, limits, tracking"
  url="https://hirli.com/dashboard/settings/usage" />

<div class="border-border flex flex-col justify-between border-b p-6">
  <h2 class="text-lg font-semibold">Feature Usage</h2>
  <p class="text-muted-foreground text-foreground/80">
    Track your feature usage and subscription limits.
  </p>
</div>

<!-- Usage Overview -->
<div class="space-y-6">
  <!-- Overview Cards -->
  <UsageOverview {usageSummary} {usageTrends} {featuresData} />

  <!-- Usage Distribution Chart -->
  <UsageDistributionChart {featuresData} />

  <!-- Refresh Controls -->
  <Card.Root>
    <Card.Content>
      <div class="mb-4 flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium">Feature Usage Details</h3>
          <p class="text-muted-foreground text-sm">Track your usage across all features</p>
        </div>
        <div class="flex gap-2">
          <Button variant="outline" size="sm" onclick={exportUsageData} disabled={isLoading}>
            <Calendar class="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Button variant="outline" size="sm" onclick={loadUsageData} disabled={isLoading}>
            {#if isLoading}
              <Loader2 class="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            {:else}
              <RefreshCw class="mr-2 h-4 w-4" />
              Refresh
            {/if}
          </Button>
        </div>
      </div>

      {#if isLoading && !usageSummary}
        <div class="flex justify-center py-8">
          <Loader2 class="text-primary h-8 w-8 animate-spin" />
        </div>
      {:else if usageSummary}
        <div class="grid gap-4 md:grid-cols-3">
          <div class="rounded-md border p-4">
            <p class="text-muted-foreground text-sm">Features Used</p>
            <h4 class="mt-1 text-2xl font-bold">{usageSummary.featuresUsed}</h4>
            <p class="text-muted-foreground mt-1 text-xs">
              of {usageSummary.totalFeatures} total features
            </p>
          </div>

          <div class="rounded-md border p-4">
            <p class="text-muted-foreground text-sm">Features at Limit</p>
            <h4 class="mt-1 text-2xl font-bold">{usageSummary.featuresAtLimit}</h4>
            <p class="text-muted-foreground mt-1 text-xs">
              {usageSummary.featuresAtLimit > 0
                ? 'Consider upgrading your plan'
                : 'You have room to grow'}
            </p>
          </div>

          <div class="rounded-md border p-4">
            <p class="text-muted-foreground text-sm">Usage Efficiency</p>
            <h4 class="mt-1 text-2xl font-bold">
              {usageSummary && usageSummary.totalFeatures > 0
                ? Math.round((usageSummary.featuresUsed / usageSummary.totalFeatures) * 100) + '%'
                : '0%'}
            </h4>
            <div class="mt-1">
              <div class="bg-muted h-1.5 w-full overflow-hidden rounded-full">
                <div
                  class="bg-primary h-full"
                  style={`width: ${
                    usageSummary && usageSummary.totalFeatures > 0
                      ? Math.round((usageSummary.featuresUsed / usageSummary.totalFeatures) * 100)
                      : 0
                  }%`}>
                </div>
              </div>
              <p class="text-muted-foreground mt-1 text-xs">
                {usageSummary && usageSummary.featuresUsed > 0
                  ? `Using ${usageSummary.featuresUsed} of ${usageSummary.totalFeatures} available features`
                  : 'No features used yet'}
              </p>
            </div>
          </div>
        </div>

        <div class="mt-4 grid gap-4 md:grid-cols-3">
          <!-- Usage Trend Card -->
          <div class="rounded-md border p-4">
            <p class="text-muted-foreground text-sm">Usage Trend</p>
            <h4 class="mt-1 flex items-center text-2xl font-bold">
              {props.data.usageTrends?.trendPercent || 0}%
              {#if props.data.usageTrends?.trendDirection === 'up'}
                <TrendingUp class="ml-2 h-5 w-5 text-green-500" />
              {:else if props.data.usageTrends?.trendDirection === 'down'}
                <TrendingDown class="ml-2 h-5 w-5 text-red-500" />
              {:else}
                <Minus class="ml-2 h-5 w-5 text-gray-500" />
              {/if}
            </h4>

            <!-- Bar visualization -->
            <div class="mt-3 flex h-2 w-full items-center gap-1">
              {#each Array(6) as _, i}
                {@const isActive =
                  i < Math.min(5, Math.abs(props.data.usageTrends?.trendPercent || 0) / 20)}
                {@const color =
                  props.data.usageTrends?.trendDirection === 'up'
                    ? 'bg-green-500'
                    : props.data.usageTrends?.trendDirection === 'down'
                      ? 'bg-red-500'
                      : 'bg-gray-300'}
                <div class={`h-full flex-1 rounded-sm ${isActive ? color : 'bg-gray-200'}`}></div>
              {/each}
            </div>

            <p class="text-muted-foreground mt-2 text-xs">
              {#if props.data.usageTrends?.trendDirection === 'up'}
                Increased usage compared to last month
              {:else if props.data.usageTrends?.trendDirection === 'down'}
                Decreased usage compared to last month
              {:else}
                Usage stable compared to last month
              {/if}
            </p>
          </div>

          <!-- Usage Comparison Card -->
          <div class="rounded-md border p-4">
            <p class="text-muted-foreground text-sm">Usage Comparison</p>
            <h4 class="mt-1 text-2xl font-bold">
              {#if usageSummary.featuresUsed > 0}
                {usageSummary.featuresUsed > usageSummary.totalFeatures / 2
                  ? 'Above Average'
                  : 'Below Average'}
              {:else}
                No Data
              {/if}
            </h4>

            {#if usageSummary.featuresUsed > 0}
              <!-- Comparison bar visualization -->
              <div class="mt-3 flex h-2 w-full items-center">
                {#if usageSummary.totalFeatures > 0}
                  {@const position = Math.min(
                    100,
                    Math.max(0, (usageSummary.featuresUsed / usageSummary.totalFeatures) * 100)
                  )}
                  <div class="relative h-full w-full rounded-sm bg-gray-200">
                    <!-- Average marker -->
                    <div
                      class="absolute bottom-0 left-1/2 top-0 w-0.5 -translate-x-1/2 transform bg-gray-400">
                    </div>
                    <!-- User position -->
                    <div
                      class="absolute bottom-0 top-0 h-2 w-2 -translate-x-1/2 -translate-y-1/4 transform rounded-full bg-blue-500"
                      style={`left: ${position}%`}>
                    </div>
                  </div>
                {/if}
              </div>
            {/if}

            <p class="text-muted-foreground mt-2 text-xs">
              {#if usageSummary.featuresUsed > 0}
                {usageSummary.featuresUsed > usageSummary.totalFeatures / 2
                  ? 'You use more features than average'
                  : 'You use fewer features than average'}
              {:else}
                Start using features to see comparison
              {/if}
            </p>
          </div>

          <!-- Feature Value Card -->
          <div class="rounded-md border p-4">
            <p class="text-muted-foreground text-sm">Most Valuable Feature</p>
            <h4 class="mt-1 text-2xl font-bold">
              {usageSummary.topFeatures && usageSummary.topFeatures.length > 0
                ? usageSummary.topFeatures[0].featureName
                : 'None'}
            </h4>

            {#if usageSummary.topFeatures && usageSummary.topFeatures.length > 0}
              <!-- Value bar visualization -->
              <div class="mt-3 flex h-2 w-full items-center">
                <div
                  class="bg-primary h-full rounded-sm"
                  style={`width: ${Math.min(100, usageSummary.topFeatures[0].percentUsed || 0)}%`}>
                </div>
              </div>
            {/if}

            <p class="text-muted-foreground mt-2 text-xs">
              {usageSummary.topFeatures && usageSummary.topFeatures.length > 0
                ? `Used ${usageSummary.topFeatures[0].used} times this month`
                : 'No feature usage recorded yet'}
            </p>
          </div>
        </div>

        {#if usageSummary.featuresAtLimit > 0}
          <div class="bg-warning/10 border-warning/20 mt-4 rounded-md border p-4 text-sm">
            <div class="flex items-start gap-3">
              <div class="text-warning mt-0.5">
                <AlertTriangle size={16} />
              </div>
              <div>
                <p class="text-warning font-medium">
                  You've reached usage limits on {usageSummary.featuresAtLimit} feature{usageSummary.featuresAtLimit >
                  1
                    ? 's'
                    : ''}.
                </p>
                <p class="text-muted-foreground mt-1">
                  You've reached the maximum usage for some features.
                </p>
                <div class="mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    class="text-warning border-warning/20 hover:bg-warning/10"
                    onclick={() => goto('/dashboard/settings/account')}>
                    View Account Settings
                  </Button>
                </div>
              </div>
            </div>
          </div>
        {/if}
      {/if}
    </Card.Content>
  </Card.Root>
</div>

<Card.Root>
  <Card.Header>
    <Card.Title>Detailed Usage</Card.Title>
    <Card.Description>View detailed usage for each feature</Card.Description>
  </Card.Header>
  <Card.Content>
    <Tabs.Root value="features">
      <Tabs.List>
        <Tabs.Trigger value="features">By Feature</Tabs.Trigger>
        <Tabs.Trigger value="history">Usage History</Tabs.Trigger>
      </Tabs.List>

      <Tabs.Content value="features" class="pt-4">
        {#if isLoading}
          <div class="flex justify-center py-8">
            <Loader2 class="text-primary h-8 w-8 animate-spin" />
          </div>
        {:else if error}
          <div class="rounded-md border p-10 text-center">
            <div
              class="bg-muted mx-auto mb-4 flex h-6 w-6 items-center justify-center rounded-full">
              <AlertTriangle class="text-muted-foreground h-6 w-6" />
            </div>
            <h3 class="mb-2 text-lg font-medium">No Usage Data Available</h3>
            <p class="text-muted-foreground mx-auto max-w-md">
              {error}
            </p>
          </div>
        {:else if featuresWithUsage.length === 0}
          <div class="rounded-md border border-dashed p-8 text-center">
            <p class="text-muted-foreground">No feature usage data available yet.</p>
            <p class="text-muted-foreground mt-2 text-sm">
              Start using features to see your usage statistics.
            </p>
            {#if featuresData.length > 0}
              <div class="mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onclick={() => {
                    // Show all features even if they don't have usage
                    featuresWithUsage = featuresData;
                  }}>
                  Show All Available Features
                </Button>
              </div>
            {/if}
          </div>
        {:else}
          <div class="grid gap-6 md:grid-cols-2">
            <!-- Feature Selection -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium">Features by Category</h3>

              <!-- Categories -->
              {#each Object.entries(featuresByCategory) as [_categoryId, categoryData]}
                <div class="space-y-2">
                  <h4 class="text-muted-foreground text-sm font-medium uppercase">
                    {categoryData.name}
                  </h4>

                  <div class="space-y-2">
                    {#each categoryData.features as feature}
                      <button
                        class={`w-full rounded-md border p-3 text-left transition-colors ${
                          selectedFeatureId === feature.id
                            ? 'border-primary bg-primary/5'
                            : 'hover:bg-muted/50'
                        }`}
                        onclick={() => {
                          selectedFeatureId = feature.id;
                          if (feature.usage && feature.usage.length > 0) {
                            selectedLimitId = feature.usage[0].limitId;
                          } else {
                            selectedLimitId = '';
                          }
                        }}>
                        <div class="flex items-center gap-3">
                          <div
                            class="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                            <span class="text-primary text-sm"
                              >{feature.icon || feature.name[0]}</span>
                          </div>
                          <div class="flex-1">
                            <h4 class="font-medium">{feature.name}</h4>
                            <div class="flex items-center justify-between">
                              <p class="text-muted-foreground text-sm">{feature.description}</p>
                              {#if feature.usage && feature.usage.length > 0 && feature.usage[0].percentUsed !== null}
                                <span
                                  class="bg-primary/10 text-primary ml-2 rounded-full px-2 py-0.5 text-xs font-medium">
                                  {Math.round(feature.usage[0].percentUsed)}%
                                </span>
                              {/if}
                            </div>
                          </div>
                        </div>
                      </button>
                    {/each}
                  </div>
                </div>
              {/each}
            </div>

            <!-- Limit Details -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">
                {selectedFeature ? selectedFeature.name : 'Select a Feature'}
              </h3>
              {#if !selectedFeature}
                <div class="rounded-md border border-dashed p-8 text-center">
                  <p class="text-muted-foreground">Select a feature to view its usage details.</p>
                </div>
              {:else if !selectedFeature.usage || selectedFeature.usage.length === 0}
                <div class="rounded-md border border-dashed p-8 text-center">
                  <p class="text-muted-foreground">No usage data available for this feature.</p>
                </div>
              {:else}
                <div class="space-y-4">
                  {#each selectedFeature.usage as usage}
                    <div class="rounded-md border p-4">
                      <div class="mb-2 flex items-center justify-between">
                        <h4 class="font-medium">{usage.limitName}</h4>
                        <div class="text-right">
                          <span class="font-medium">
                            {usage.used} / {usage.limit === 'unlimited' ? 'Unlimited' : usage.limit}
                          </span>
                          {#if usage.placeholder}
                            <p class="text-muted-foreground text-xs">No usage yet</p>
                          {:else if usage.percentUsed !== null && usage.percentUsed !== undefined}
                            <p class="text-muted-foreground text-xs">
                              {Math.round(usage.percentUsed)}% used
                            </p>
                          {/if}
                        </div>
                      </div>
                      <p class="text-muted-foreground mb-3 text-sm">
                        {usage.description || ''}
                      </p>
                      {#if !usage.placeholder && usage.percentUsed !== null && usage.percentUsed !== undefined}
                        <div class="bg-muted h-2 w-full overflow-hidden rounded-full">
                          <div
                            class={`h-full ${usage.percentUsed >= 90 ? 'bg-destructive' : usage.percentUsed >= 75 ? 'bg-warning' : 'bg-primary'}`}
                            style={`width: ${Math.min(100, usage.percentUsed)}%`}>
                          </div>
                        </div>
                      {:else if usage.placeholder}
                        <div class="bg-muted h-2 w-full overflow-hidden rounded-full">
                          <div class="bg-primary/30 h-full" style="width: 0%"></div>
                        </div>
                      {/if}

                      {#if usage.period}
                        <div class="text-muted-foreground mt-3 text-xs">
                          {#if usage.period.includes('-')}
                            Period: {new Date(usage.period + '-01').toLocaleDateString(undefined, {
                              year: 'numeric',
                              month: 'long',
                            })}
                          {:else}
                            Period: {usage.period}
                          {/if}
                        </div>
                      {/if}

                      <div class="mt-4 flex flex-wrap gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onclick={() => {
                            selectedLimitId = usage.limitId;
                          }}>
                          View History
                        </Button>

                        {#if !usage.placeholder && usage.percentUsed >= 75 && usage.limit !== 'unlimited'}
                          <Button
                            variant={usage.percentUsed >= 90 ? 'destructive' : 'outline'}
                            size="sm"
                            class={usage.percentUsed >= 75 && usage.percentUsed < 90
                              ? 'text-warning border-warning/20 hover:bg-warning/10'
                              : ''}
                            onclick={() => goto('/dashboard/settings/account')}>
                            {usage.percentUsed >= 90 ? 'View Limits' : 'Check Usage'}
                          </Button>
                        {/if}
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          </div>
        {/if}
      </Tabs.Content>

      <Tabs.Content value="history" class="pt-4">
        <div class="space-y-6">
          {#if selectedFeatureId && selectedLimitId}
            <div class="mb-6">
              <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h3 class="text-lg font-medium">Usage History</h3>
                  <p class="text-muted-foreground text-sm">
                    Track your usage over time for {selectedFeature?.name || 'selected feature'}
                  </p>
                </div>
                <div class="flex flex-wrap items-center gap-2">
                  <select
                    class="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-3 py-1 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
                    bind:value={selectedFeatureId}
                    onchange={(e) => {
                      const target = e.target as HTMLSelectElement;
                      if (featuresData.find((f) => f.id === target.value)?.usage?.length > 0) {
                        selectedLimitId = featuresData.find((f) => f.id === target.value)!.usage[0]
                          .limitId;
                      }
                    }}>
                    <option value="" disabled>Select Feature</option>
                    {#each featuresData.filter((f) => f.usage && f.usage.length > 0) as feature}
                      <option value={feature.id}>{feature.name}</option>
                    {/each}
                  </select>

                  {#if selectedFeature && selectedFeature.usage && selectedFeature.usage.length > 0}
                    <select
                      class="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-3 py-1 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
                      bind:value={selectedLimitId}>
                      <option value="" disabled>Select Limit</option>
                      {#each selectedFeature.usage as usage}
                        <option value={usage.limitId}>{usage.limitName}</option>
                      {/each}
                    </select>
                  {/if}

                  <div class="ml-auto flex items-center gap-2">
                    <div class="relative">
                      <Button variant="outline" size="sm" class="flex h-8 items-center gap-1">
                        <Calendar class="h-4 w-4" />
                        <span>Date Range</span>
                        <ChevronDown class="h-4 w-4" />
                      </Button>
                      <!-- Dropdown would go here in a real implementation -->
                    </div>
                    <div class="flex items-center gap-1 overflow-hidden rounded-md border">
                      <Button
                        variant={timeRange === '30d' ? 'default' : 'ghost'}
                        size="sm"
                        class="h-8 rounded-none"
                        onclick={() => (timeRange = '30d')}>
                        30d
                      </Button>
                      <Button
                        variant={timeRange === '90d' ? 'default' : 'ghost'}
                        size="sm"
                        class="h-8 rounded-none"
                        onclick={() => (timeRange = '90d')}>
                        90d
                      </Button>
                      <Button
                        variant={timeRange === '1y' ? 'default' : 'ghost'}
                        size="sm"
                        class="h-8 rounded-none"
                        onclick={() => (timeRange = '1y')}>
                        1y
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <UsageHistoryChart featureId={selectedFeatureId} limitId={selectedLimitId} />

            <div class="mt-8 rounded-md border p-4">
              <h4 class="mb-2 text-sm font-medium">Usage Tips</h4>
              <ul class="text-muted-foreground space-y-2 text-sm">
                <li class="flex items-start gap-2">
                  <CheckCircle size={16} class="mt-0.5" />
                  <span
                    >Usage is tracked on a monthly basis and resets at the beginning of each month.</span>
                </li>
                <li class="flex items-start gap-2">
                  <CheckCircle size={16} class="mt-0.5" />
                  <span>Usage limits are based on your account type and settings.</span>
                </li>
                <li class="flex items-start gap-2">
                  <CheckCircle size={16} class="mt-0.5" />
                  <span
                    >Contact support if you need temporary limit increases for special projects.</span>
                </li>
              </ul>
            </div>
          {:else}
            <div class="rounded-md border border-dashed p-8 text-center">
              <p class="text-muted-foreground">Select a feature and limit to view usage history.</p>
            </div>
          {/if}
        </div>
      </Tabs.Content>
    </Tabs.Root>
  </Card.Content>
</Card.Root>
