/**
 * Job Matching Utilities for Automation
 * 
 * This module provides utilities for fast job relevance matching
 * using pre-processed job data and profile information.
 */

export interface JobMatchCriteria {
  profileId: string;
  skills: string[];
  experienceYears: number;
  jobTitles: string[];
  salaryMin?: number;
  salaryMax?: number;
  remotePreference: 'any' | 'remote' | 'hybrid' | 'onsite';
  companySizePreference: string[];
  excludeCompanies: string[];
  preferredCompanies: string[];
}

export interface JobMatchResult {
  jobId: string;
  overallScore: number;
  skillsMatch: {
    matchedSkills: string[];
    missingSkills: string[];
    skillsScore: number;
  };
  experienceMatch: {
    isMatch: boolean;
    experienceScore: number;
    requiredYears: number;
    candidateYears: number;
  };
  titleMatch: {
    isMatch: boolean;
    titleScore: number;
    normalizedTitle: string;
  };
  salaryMatch: {
    isMatch: boolean;
    salaryScore: number;
  };
  locationMatch: {
    isMatch: boolean;
    locationScore: number;
  };
  companyMatch: {
    isPreferred: boolean;
    isExcluded: boolean;
    companyScore: number;
  };
  reasons: string[];
}

/**
 * Fast job matching query builder
 * Builds optimized database queries using indexed fields
 */
export function buildJobMatchQuery(criteria: JobMatchCriteria) {
  const whereConditions: any = {
    isActive: true,
    isAnalyzed: true, // Only match analyzed jobs
  };

  // Experience level filtering
  if (criteria.experienceYears >= 0) {
    const seniorityLevel = getSeniorityLevel(criteria.experienceYears);
    whereConditions.OR = [
      { seniorityLevel: seniorityLevel },
      { yearsOfExperience: { lte: criteria.experienceYears + 2 } }, // Allow some flexibility
    ];
  }

  // Salary filtering
  if (criteria.salaryMin || criteria.salaryMax) {
    whereConditions.AND = whereConditions.AND || [];
    
    if (criteria.salaryMin) {
      whereConditions.AND.push({
        OR: [
          { salaryMax: { gte: criteria.salaryMin } },
          { salaryMin: null }, // Include jobs without salary info
        ]
      });
    }
    
    if (criteria.salaryMax) {
      whereConditions.AND.push({
        OR: [
          { salaryMin: { lte: criteria.salaryMax } },
          { salaryMax: null }, // Include jobs without salary info
        ]
      });
    }
  }

  // Remote work filtering
  if (criteria.remotePreference !== 'any') {
    if (criteria.remotePreference === 'remote') {
      whereConditions.isRemoteFriendly = true;
    } else if (criteria.remotePreference === 'onsite') {
      whereConditions.isRemoteFriendly = false;
    }
    // For hybrid, we include both remote-friendly and non-remote jobs
  }

  // Company size filtering
  if (criteria.companySizePreference.length > 0) {
    whereConditions.companySize = {
      in: criteria.companySizePreference
    };
  }

  // Exclude companies
  if (criteria.excludeCompanies.length > 0) {
    whereConditions.company = {
      notIn: criteria.excludeCompanies
    };
  }

  // Skills matching (using array overlap)
  if (criteria.skills.length > 0) {
    whereConditions.techStack = {
      hasSome: criteria.skills
    };
  }

  return whereConditions;
}

/**
 * Calculate job match score
 */
export function calculateJobMatchScore(job: any, criteria: JobMatchCriteria): JobMatchResult {
  let overallScore = 0;
  const reasons: string[] = [];

  // Skills matching (40% weight)
  const skillsMatch = calculateSkillsMatch(job.techStack || job.skills || [], criteria.skills);
  overallScore += skillsMatch.skillsScore * 0.4;
  if (skillsMatch.matchedSkills.length > 0) {
    reasons.push(`Matched ${skillsMatch.matchedSkills.length} skills`);
  }

  // Experience matching (25% weight)
  const experienceMatch = calculateExperienceMatch(job.yearsOfExperience, criteria.experienceYears);
  overallScore += experienceMatch.experienceScore * 0.25;
  if (experienceMatch.isMatch) {
    reasons.push('Experience level matches');
  }

  // Title matching (20% weight)
  const titleMatch = calculateTitleMatch(job.normalizedTitle || job.title, criteria.jobTitles);
  overallScore += titleMatch.titleScore * 0.2;
  if (titleMatch.isMatch) {
    reasons.push('Job title matches profile');
  }

  // Salary matching (10% weight)
  const salaryMatch = calculateSalaryMatch(job, criteria);
  overallScore += salaryMatch.salaryScore * 0.1;
  if (salaryMatch.isMatch) {
    reasons.push('Salary meets requirements');
  }

  // Location/Remote matching (5% weight)
  const locationMatch = calculateLocationMatch(job, criteria);
  overallScore += locationMatch.locationScore * 0.05;
  if (locationMatch.isMatch) {
    reasons.push('Location preference matches');
  }

  // Company preference bonus/penalty
  const companyMatch = calculateCompanyMatch(job.company, criteria);
  if (companyMatch.isPreferred) {
    overallScore += 10; // Bonus for preferred companies
    reasons.push('Preferred company');
  }
  if (companyMatch.isExcluded) {
    overallScore = 0; // Exclude completely
    reasons.push('Company excluded');
  }

  return {
    jobId: job.id,
    overallScore: Math.min(100, Math.max(0, overallScore)),
    skillsMatch,
    experienceMatch,
    titleMatch,
    salaryMatch,
    locationMatch,
    companyMatch,
    reasons
  };
}

/**
 * Helper functions for individual matching components
 */

function getSeniorityLevel(experienceYears: number): string {
  if (experienceYears <= 2) return 'entry';
  if (experienceYears <= 5) return 'mid';
  if (experienceYears <= 8) return 'senior';
  if (experienceYears <= 12) return 'lead';
  return 'principal';
}

function calculateSkillsMatch(jobSkills: string[], profileSkills: string[]) {
  const jobSkillsLower = jobSkills.map(s => s.toLowerCase());
  const profileSkillsLower = profileSkills.map(s => s.toLowerCase());
  
  const matchedSkills = profileSkillsLower.filter(skill => 
    jobSkillsLower.some(jobSkill => 
      jobSkill.includes(skill) || skill.includes(jobSkill)
    )
  );
  
  const missingSkills = jobSkillsLower.filter(skill => 
    !profileSkillsLower.some(profileSkill => 
      skill.includes(profileSkill) || profileSkill.includes(skill)
    )
  );

  const skillsScore = jobSkills.length > 0 
    ? (matchedSkills.length / jobSkills.length) * 100 
    : 50; // Neutral score if no skills listed

  return {
    matchedSkills,
    missingSkills,
    skillsScore
  };
}

function calculateExperienceMatch(requiredYears: number, candidateYears: number) {
  const isMatch = candidateYears >= requiredYears - 1; // Allow 1 year flexibility
  const experienceScore = isMatch ? 100 : Math.max(0, 100 - (requiredYears - candidateYears) * 20);

  return {
    isMatch,
    experienceScore,
    requiredYears,
    candidateYears
  };
}

function calculateTitleMatch(jobTitle: string, profileTitles: string[]) {
  const jobTitleLower = jobTitle.toLowerCase();
  const isMatch = profileTitles.some(title => 
    jobTitleLower.includes(title.toLowerCase()) || 
    title.toLowerCase().includes(jobTitleLower)
  );

  return {
    isMatch,
    titleScore: isMatch ? 100 : 30, // Some base score even without exact match
    normalizedTitle: jobTitle
  };
}

function calculateSalaryMatch(job: any, criteria: JobMatchCriteria) {
  const jobSalaryMin = job.salaryMin;
  const jobSalaryMax = job.salaryMax;
  
  let isMatch = true;
  let salaryScore = 100;

  // Check if job meets minimum salary requirement
  if (criteria.salaryMin && jobSalaryMax && jobSalaryMax < criteria.salaryMin) {
    isMatch = false;
    salaryScore = 0;
  }

  // Check if job exceeds maximum salary preference
  if (criteria.salaryMax && jobSalaryMin && jobSalaryMin > criteria.salaryMax) {
    isMatch = false;
    salaryScore = 0;
  }

  // If no salary info, give neutral score
  if (!jobSalaryMin && !jobSalaryMax) {
    salaryScore = 70;
  }

  return {
    isMatch,
    salaryScore
  };
}

function calculateLocationMatch(job: any, criteria: JobMatchCriteria) {
  let isMatch = true;
  let locationScore = 100;

  if (criteria.remotePreference === 'remote' && !job.isRemoteFriendly) {
    isMatch = false;
    locationScore = 20;
  } else if (criteria.remotePreference === 'onsite' && job.isRemoteFriendly) {
    locationScore = 80; // Slight penalty but not disqualifying
  }

  return {
    isMatch,
    locationScore
  };
}

function calculateCompanyMatch(company: string, criteria: JobMatchCriteria) {
  const isPreferred = criteria.preferredCompanies.includes(company);
  const isExcluded = criteria.excludeCompanies.includes(company);
  
  let companyScore = 100;
  if (isPreferred) companyScore = 110;
  if (isExcluded) companyScore = 0;

  return {
    isPreferred,
    isExcluded,
    companyScore
  };
}

/**
 * Job preprocessing utilities
 * These should be run when jobs are scraped/updated
 */

export function normalizeJobTitle(title: string): string {
  // Remove common prefixes/suffixes and normalize
  return title
    .toLowerCase()
    .replace(/\b(sr|senior|jr|junior|lead|principal)\b/g, '')
    .replace(/\b(engineer|developer|dev)\b/g, 'engineer')
    .replace(/\s+/g, ' ')
    .trim();
}

export function extractTechStack(description: string, title: string): string[] {
  const techKeywords = [
    'javascript', 'typescript', 'python', 'java', 'c#', 'c++', 'go', 'rust', 'php',
    'react', 'vue', 'angular', 'svelte', 'node.js', 'express', 'fastapi', 'django',
    'postgresql', 'mysql', 'mongodb', 'redis', 'elasticsearch',
    'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'terraform',
    'git', 'jenkins', 'github', 'gitlab', 'jira'
  ];

  const text = (description + ' ' + title).toLowerCase();
  return techKeywords.filter(tech => text.includes(tech));
}

export function determineSeniorityLevel(title: string, yearsRequired: number): string {
  const titleLower = title.toLowerCase();
  
  if (titleLower.includes('principal') || titleLower.includes('staff')) return 'principal';
  if (titleLower.includes('lead') || titleLower.includes('senior')) return 'senior';
  if (titleLower.includes('junior') || titleLower.includes('jr')) return 'entry';
  
  // Fallback to years of experience
  return getSeniorityLevel(yearsRequired);
}
