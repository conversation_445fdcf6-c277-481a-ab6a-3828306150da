<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Form from '$lib/components/ui/form/index.js';
  import * as Select from '$lib/components/ui/select/index.js';
  import * as Switch from '$lib/components/ui/switch/index.js';

  const { form, formData } = $props<{ form: any; formData: any }>();

  const visibilityOptions = [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
  ];
</script>

<Card.Root>
  <Card.Header class="p-6">
    <Card.Title>Privacy Settings</Card.Title>
    <Card.Description>Control your privacy and data preferences.</Card.Description>
  </Card.Header>
  <Card.Content class="space-y-6 p-6 pt-0">
    <Form.Field {form} name="profileVisibility">
      <Form.Control>
        {#snippet children({ props })}
          <div class="space-y-0.5">
            <Form.Label>Profile Visibility</Form.Label>
            <Form.Description>Who can see your profile information</Form.Description>
          </div>
          <Select.Root
            {...props}
            type="single"
            value={$formData.profileVisibility || 'public'}
            onValueChange={(value) => {
              formData.update((f: any) => ({ ...f, profileVisibility: value }));
              // Submit the form to save changes to the database
              setTimeout(() => {
                const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
                submitButton?.click();
              }, 100);
            }}>
            <Select.Trigger class="w-full">
              <Select.Value placeholder="Select visibility" />
            </Select.Trigger>
            <Select.Content class="max-h-60">
              <Select.Group>
                {#each visibilityOptions as option (option.value)}
                  <Select.Item value={option.value} label={option.label}
                    >{option.label}</Select.Item>
                {/each}
              </Select.Group>
            </Select.Content>
          </Select.Root>
        {/snippet}
      </Form.Control>
      <Form.Description>Who can see your profile information</Form.Description>
      <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="allowDataCollection">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Data Collection</div>
          <Form.Description
            >Allow us to collect usage data to improve your experience</Form.Description>
        </div>
        <Form.Control>
          <Switch.Root
            checked={Boolean($formData.allowDataCollection)}
            onCheckedChange={(checked) => {
              formData.update((f: any) => ({ ...f, allowDataCollection: checked }));
              // Submit the form to save changes to the database
              setTimeout(() => {
                const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
                submitButton?.click();
              }, 100);
            }} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="allowThirdPartySharing">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">Third-Party Data Sharing</div>
          <Form.Description>Allow sharing your data with trusted partners</Form.Description>
        </div>
        <Form.Control>
          <Switch.Root
            checked={Boolean($formData.allowThirdPartySharing)}
            onCheckedChange={(checked) => {
              formData.update((f: any) => ({ ...f, allowThirdPartySharing: checked }));
              // Submit the form to save changes to the database
              setTimeout(() => {
                const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
                submitButton?.click();
              }, 100);
            }} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>
  </Card.Content>
</Card.Root>
