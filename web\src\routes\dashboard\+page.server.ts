// src/routes/dashboard/+page.server.ts
import { verifySessionToken } from '$lib/server/auth';
import type { User } from '$lib/types.js'; // Assuming you have a User type defined
// Assuming you have a User type defined
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { Roles } from '$lib/config/roles.js';
import { calculateProfileCompletion } from '$lib/utils/profileHelpers';

// Using the shared Prisma client from $lib/server/prisma
export const load = async ({ cookies, locals }: { cookies: any; locals: { user?: User } }) => {
  const token = cookies.get('auth_token');
  const user = token && verifySessionToken(token);

  // Set locals.user if authenticated
  if (await user) {
    locals.user = (await user)
      ? {
          id: (await user).id,
          name: (await user).name || (await user).email,
          email: (await user).email,
          role: (await user).role,
        }
      : undefined;
  }

  if (!locals.user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Fetch the user's teams from the database
  const userTeams = await prisma.team.findMany({
    where: { members: { some: { id: locals.user.id } } },
  });

  // Fetch recent job searches
  const searches = await prisma.jobSearch.findMany({
    where: { userId: locals.user.id },
    orderBy: { createdAt: 'desc' },
    take: 10,
  });

  // Fetch subscription usage data
  const used = await prisma.documentSubmission.count({
    where: {
      userId: locals.user.id,
      createdAt: { gte: new Date(new Date().setDate(1)) },
    },
  });

  const role = locals.user.role as keyof typeof Roles;
  const limit = Roles[role]?.limits.jobSubmissionsPerMonth ?? null;
  const remaining = limit !== null ? Math.max(0, limit - used) : null;

  // Fetch job match data - handle case where model might not exist yet
  let jobMatches = [];

  try {
    // Check if the job_match_result model exists
    if (prisma.job_match_result) {
      jobMatches = await prisma.job_match_result.findMany({
        where: { userId: locals.user.id },
        include: { job_listing: true },
        orderBy: { matchScore: 'desc' },
        take: 50,
      });
    }
  } catch (error) {
    console.log('Job match data not available:', error.message);
    // Continue with empty array if the model doesn't exist
  }

  // Calculate match statistics
  const matchStats = {
    total: jobMatches.length,
    highMatches: jobMatches.filter((match) => match.matchScore >= 0.8).length,
    mediumMatches: jobMatches.filter((match) => match.matchScore >= 0.6 && match.matchScore < 0.8)
      .length,
    lowMatches: jobMatches.filter((match) => match.matchScore < 0.6).length,
    applied: jobMatches.filter((match) => match.applied).length,
    notApplied: jobMatches.filter((match) => !match.applied).length,
  };

  // Fetch profiles and calculate completion
  const profiles = await prisma.profile.findMany({
    where: {
      OR: [{ userId: locals.user.id }, { team: { members: { some: { userId: locals.user.id } } } }],
    },
    include: {
      data: true,
      documents: true,
    },
  });

  // Calculate profile completion percentages using shared utility
  const profileStats = profiles.map((profile) => {
    let completionPercentage = 0;

    // Use shared utility if profile has data
    if (profile.data?.data) {
      // Parse profile data properly - it might be a JSON string
      let profileData = profile.data.data;
      if (typeof profileData === 'string') {
        try {
          profileData = JSON.parse(profileData);
        } catch (e) {
          console.error('Error parsing profile data JSON:', e);
          profileData = {};
        }
      }
      completionPercentage = calculateProfileCompletion(profileData as any);
    }

    return {
      id: profile.id,
      name: profile.name,
      completionPercentage,
      documentCount: profile.documents.length,
    };
  });

  // Get recent applications - handle case where model might not exist yet
  let recentApplications = [];
  let applicationsByStatus = [];

  try {
    // Check if the jobApplication model exists
    if (prisma.application) {
      recentApplications = await prisma.application.findMany({
        where: { userId: locals.user.id },
        orderBy: { createdAt: 'desc' },
        take: 5,
        include: {
          job: true,
        },
      });

      // Get application statistics by status
      applicationsByStatus = await prisma.application.groupBy({
        by: ['status'],
        where: { userId: locals.user.id },
        _count: true,
      });
    }
  } catch (error) {
    console.log('Job application data not available:', error.message);
    // Continue with empty arrays if the model doesn't exist
  }

  // Format application stats
  const applicationStats = {
    total: Array.isArray(applicationsByStatus)
      ? applicationsByStatus.reduce((sum, item) => sum + item._count, 0)
      : 0,
    byStatus: Array.isArray(applicationsByStatus)
      ? Object.fromEntries(applicationsByStatus.map((item) => [item.status, item._count]))
      : {},
  };

  return {
    user: locals.user,
    teams: userTeams,
    searches,
    usage: { used, limit, remaining },
    matchStats,
    profileStats,
    recentApplications,
    applicationStats,
    // Include a few top matches for the dashboard
    topMatches: jobMatches.slice(0, 5),
  };
};
