<script lang="ts">
  import { toast } from 'svelte-sonner';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import { Slider } from '$lib/components/ui/slider';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Badge } from '$lib/components/ui/badge';
  import { Label } from '$lib/components/ui/label';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Select from '$lib/components/ui/select';
  import SEO from '$components/shared/SEO.svelte';
  import {
    Plus,
    Play,
    CheckCircle,
    AlertTriangle,
    X,
    Settings,
    Target,
    Building,
    Filter,
  } from 'lucide-svelte';

  import AutomationRunSheet from './AutomationRunSheet.svelte';
  import AutomationRunsTab from './AutomationRunsTab.svelte';
  import ProfilesTab from './ProfilesTab.svelte';
  import FeatureGuard from '$components/features/EnhancedFeatureGuard.svelte';
  import { getProfileData } from '$lib/utils/profile';
  import { checkAutomationEligibility } from '$lib/utils/profileHelpers';
  import { writable } from 'svelte/store';
  import * as Dialog from '$lib/components/ui/dialog';

  const { data } = $props();

  let profiles = $state(data.profiles || []);
  // Convert automationRuns to a proper Svelte store
  const automationRuns = writable(data.automationRuns || []);

  // Sheet state
  let selectedRun = $state(null);
  let isSheetOpen = $state(false);

  // State for new automation run
  let selectedProfileId = $state('');
  let selectedKeywords = $state('');
  let selectedLocation = $state('');
  let createDialogOpen = $state(false);
  let isCreatingRun = $state(false);

  // Automation settings
  let maxJobsToApply = $state(10);
  let minMatchScore = $state(70);
  let autoApplyEnabled = $state(false);
  let salaryMin = $state('');
  let salaryMax = $state('');
  let experienceLevelMin = $state('');
  let experienceLevelMax = $state('');
  let jobTypes = $state<string[]>([]);
  let remotePreference = $state('any');
  let companySizePreference = $state<string[]>([]);
  let excludeCompanies = $state<string[]>([]);
  let preferredCompanies = $state<string[]>([]);

  // UI state
  let newExcludeCompany = $state('');
  let newPreferredCompany = $state('');

  // Computed properties
  const selectedProfile = $derived(() => {
    return profiles.find((p) => p.id === selectedProfileId);
  });

  const isProfileEligible = $derived(() => {
    if (!selectedProfile()) return false;
    const eligibility = checkAutomationEligibility(selectedProfile());
    return eligibility.isEligible;
  });

  // Form validation
  const isFormValid = $derived(() => {
    // Must have a profile selected and it must be eligible
    if (!selectedProfileId || !isProfileEligible) return false;

    // Must have either keywords or location (or both)
    if (!selectedKeywords.trim() && !selectedLocation.trim()) return false;

    // Salary validation
    if (salaryMin && salaryMax && parseInt(salaryMin) > parseInt(salaryMax)) return false;

    // Experience validation
    if (
      experienceLevelMin &&
      experienceLevelMax &&
      parseInt(experienceLevelMin) > parseInt(experienceLevelMax)
    )
      return false;

    return true;
  });

  // Salary options (in thousands)
  const salaryOptions = [
    { value: '30000', label: '$30,000' },
    { value: '40000', label: '$40,000' },
    { value: '50000', label: '$50,000' },
    { value: '60000', label: '$60,000' },
    { value: '70000', label: '$70,000' },
    { value: '80000', label: '$80,000' },
    { value: '90000', label: '$90,000' },
    { value: '100000', label: '$100,000' },
    { value: '120000', label: '$120,000' },
    { value: '140000', label: '$140,000' },
    { value: '160000', label: '$160,000' },
    { value: '180000', label: '$180,000' },
    { value: '200000', label: '$200,000' },
    { value: '250000', label: '$250,000+' },
  ];

  // Experience level options
  const experienceOptions = [
    { value: '0', label: 'Entry Level (0 years)' },
    { value: '1', label: '1 year' },
    { value: '2', label: '2 years' },
    { value: '3', label: '3 years' },
    { value: '4', label: '4 years' },
    { value: '5', label: '5 years' },
    { value: '7', label: '7 years' },
    { value: '10', label: '10 years' },
    { value: '15', label: '15+ years' },
  ];

  // Get suggestions from selected profile
  const profileSuggestions = $derived(() => {
    if (!selectedProfile()?.data?.data) return null;

    const profileData = selectedProfile().data.data as any;

    // Extract job title suggestions from work experience
    const jobTitles =
      profileData.workExperience?.map((exp: any) => exp.title).filter(Boolean) || [];

    // Extract skills for keyword suggestions
    const skills = profileData.skillsData?.list || profileData.skills || [];

    // Calculate total experience years
    let totalExperience = 0;
    if (profileData.workExperience) {
      profileData.workExperience.forEach((exp: any) => {
        if (exp.startDate && exp.endDate) {
          const start = new Date(exp.startDate);
          const end = exp.current ? new Date() : new Date(exp.endDate);
          const years = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365);
          totalExperience += years;
        }
      });
    }

    // Get location from profile
    const location = profileData.personalInfo?.city || profileData.personalInfo?.address || '';

    return {
      jobTitles: [...new Set(jobTitles)].slice(0, 3), // Top 3 unique job titles
      skills: Array.isArray(skills) ? skills.slice(0, 5) : [], // Top 5 skills
      experienceYears: Math.floor(totalExperience),
      location,
    };
  });

  // Auto-fill suggestions when profile is selected
  function applySuggestions() {
    const suggestions = profileSuggestions();
    if (!suggestions) return;

    // Suggest keywords based on latest job title and top skills
    if (suggestions.jobTitles.length > 0 && suggestions.skills.length > 0) {
      const suggestedKeywords = [suggestions.jobTitles[0], ...suggestions.skills.slice(0, 2)].join(
        ', '
      );
      selectedKeywords = suggestedKeywords;
    }

    // Suggest location
    if (suggestions.location) {
      selectedLocation = suggestions.location;
    }

    // Suggest experience range based on calculated experience
    if (suggestions.experienceYears > 0) {
      const minExp = Math.max(0, suggestions.experienceYears - 2);
      const maxExp = suggestions.experienceYears + 3;
      experienceLevelMin = minExp.toString();
      experienceLevelMax = maxExp.toString();
    }
  }

  // Function to create a new automation run with advanced settings
  async function createAutomationRun() {
    if (!selectedProfileId) {
      toast.error('Please select a profile');
      return;
    }

    if (!isProfileEligible) {
      toast.error('Selected profile is not eligible for automation');
      return;
    }

    isCreatingRun = true;

    try {
      // Call the API to create a new automation run with advanced settings
      const response = await fetch('/api/automation/runs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profileId: selectedProfileId,
          keywords: selectedKeywords,
          location: selectedLocation,
          maxJobsToApply,
          minMatchScore,
          autoApplyEnabled,
          salaryMin: salaryMin ? parseInt(salaryMin) : null,
          salaryMax: salaryMax ? parseInt(salaryMax) : null,
          experienceLevelMin: experienceLevelMin ? parseInt(experienceLevelMin) : null,
          experienceLevelMax: experienceLevelMax ? parseInt(experienceLevelMax) : null,
          jobTypes,
          remotePreference,
          companySizePreference,
          excludeCompanies,
          preferredCompanies,
          specifications: {
            // Store additional configuration in JSON field
            advancedFiltering: true,
            profileMatchingEnabled: true,
            intelligentScoring: true,
          },
        }),
      });

      if (response.ok) {
        const newRun = await response.json();
        toast.success('Automation run created successfully');
        toast.info('The automation system is processing your request');

        createDialogOpen = false;
        resetForm();

        // Redirect to the automation run detail page
        window.location.href = `/dashboard/automation/${newRun.id}`;
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to create automation run');
      }
    } catch (error) {
      console.error('Error creating automation run:', error);
      toast.error('An error occurred while creating the automation run');
    } finally {
      isCreatingRun = false;
    }
  }

  // Function to reset the form
  function resetForm() {
    selectedProfileId = '';
    selectedKeywords = '';
    selectedLocation = '';
    maxJobsToApply = 10;
    minMatchScore = 70;
    autoApplyEnabled = false;
    salaryMin = '';
    salaryMax = '';
    experienceLevelMin = '';
    experienceLevelMax = '';
    jobTypes = [];
    remotePreference = 'any';
    companySizePreference = [];
    excludeCompanies = [];
    preferredCompanies = [];
    newExcludeCompany = '';
    newPreferredCompany = '';
  }

  // Function to handle refreshing a run
  function handleRunRefresh(updatedRun: any) {
    automationRuns.update((runs) =>
      runs.map((run: any) => (run.id === updatedRun.id ? updatedRun : run))
    );
  }
</script>

<SEO
  title="Job Automation | Hirli"
  description="Automate your job search and application process with Hirli's intelligent automation tools."
  keywords="job automation, automated job search, job application automation, resume matching, career automation, job search tools" />

<div class="flex w-full flex-col">
  <div class="flex flex-col gap-8 p-4">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold">Automation</h1>
        <p class="text-muted-foreground text-sm">
          Automate your job search and application process
        </p>
      </div>
      <FeatureGuard
        userData={data.user}
        featureId="automation"
        limitId="automation_runs_per_month"
        showUpgradePrompt={true}
        fallbackMessage="Automation features are not available in your current plan">
        <Button variant="default" onclick={() => (createDialogOpen = true)}>
          <Play class="mr-2 h-4 w-4" />
          New Automation Run
        </Button>
      </FeatureGuard>
    </div>
  </div>

  <Tabs.Root value="runs">
    <Tabs.List class="flex flex-row gap-2 divide-x rounded-none px-2">
      <Tabs.Trigger value="runs" class="flex-1">Automation Runs</Tabs.Trigger>
      <Tabs.Trigger value="profiles" class="flex-1">Available Profiles</Tabs.Trigger>
    </Tabs.List>

    <Tabs.Content value="runs" class="mt-0 p-4">
      <AutomationRunsTab
        userData={data.user}
        {automationRuns}
        onRunSelect={(run) => {
          selectedRun = run;
          isSheetOpen = true;
        }}
        onCreateRun={() => (createDialogOpen = true)} />
    </Tabs.Content>

    <Tabs.Content value="profiles" class="mt-0 p-4">
      <ProfilesTab
        userData={data.user}
        {profiles}
        onProfileSelect={(profileId) => {
          selectedProfileId = profileId;
          createDialogOpen = true;
        }} />
    </Tabs.Content>
  </Tabs.Root>
</div>

<!-- Create Automation Run Dialog -->
<Dialog.Root bind:open={createDialogOpen}>
  <Dialog.Overlay />
  <Dialog.Content class="max-h-[90vh] max-w-4xl overflow-y-auto">
    <FeatureGuard
      userData={data.user}
      featureId="automation"
      limitId="automation_runs_per_month"
      showUpgradePrompt={true}
      fallbackMessage="Automation features are not available in your current plan">
      <Dialog.Header>
        <Dialog.Title>Configure Automation Run</Dialog.Title>
        <Dialog.Description>
          Set up detailed automation specifications for intelligent job matching and application.
        </Dialog.Description>
      </Dialog.Header>

      <div class="grid gap-6 py-4">
        <!-- Profile Selection -->
        <div class="grid gap-2">
          <div class="flex items-center justify-between">
            <label for="profile" class="text-sm font-medium">Profile *</label>
            <Button
              variant="ghost"
              size="sm"
              onclick={() => (window.location.href = '/dashboard/settings/profile')}
              class="text-xs text-blue-500 hover:text-blue-400">
              <Plus class="mr-1 h-3 w-3" />
              Manage Profiles
            </Button>
          </div>
          <select
            id="profile"
            bind:value={selectedProfileId}
            class="border-input bg-background w-full rounded-md border px-3 py-2">
            <option value="">Select a profile</option>
            {#each profiles as profile}
              <option value={profile.id}>
                {getProfileData(profile).fullName || 'Unnamed Profile'}
              </option>
            {/each}
          </select>
        </div>

        <!-- Profile Eligibility Check -->
        {#if selectedProfileId}
          {@const selectedProfile = profiles.find((p) => p.id === selectedProfileId)}
          {#if selectedProfile}
            {@const eligibility = checkAutomationEligibility(selectedProfile)}
            <div class="rounded-lg border p-4">
              <div class="mb-2 flex items-center gap-2">
                {#if eligibility.isEligible}
                  <CheckCircle class="h-5 w-5 text-green-500" />
                  <span class="font-medium text-green-700">Profile Eligible for Automation</span>
                {:else}
                  <AlertTriangle class="h-5 w-5 text-orange-500" />
                  <span class="font-medium text-orange-700">Profile Needs Completion</span>
                {/if}
              </div>

              <div class="mb-3">
                <div class="mb-1 flex items-center justify-between text-sm">
                  <span>Profile Completion</span>
                  <span>{eligibility.completionPercentage}%</span>
                </div>
                <Progress value={eligibility.completionPercentage} max={100} />
              </div>

              {#if !eligibility.isEligible}
                <div class="space-y-1">
                  <p class="text-sm font-medium text-gray-700">Missing Requirements:</p>
                  {#each eligibility.missingRequirements as requirement}
                    <div class="flex items-center gap-2 text-sm text-gray-600">
                      <X class="h-3 w-3 text-red-500" />
                      {requirement}
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          {/if}
        {/if}

        <!-- Basic Search Criteria -->
        <div class="grid grid-cols-2 gap-4">
          <div class="grid gap-2">
            <div class="flex items-center justify-between">
              <label for="keywords" class="text-sm font-medium">Keywords</label>
              {#if profileSuggestions() && profileSuggestions().jobTitles.length > 0}
                <Button
                  variant="ghost"
                  size="sm"
                  onclick={applySuggestions}
                  class="text-xs text-blue-500 hover:text-blue-400">
                  Use Profile Suggestions
                </Button>
              {/if}
            </div>
            <Input
              id="keywords"
              bind:value={selectedKeywords}
              placeholder="e.g. Software Engineer, Developer" />
            {#if profileSuggestions() && profileSuggestions().jobTitles.length > 0}
              <p class="text-xs text-gray-500">
                Suggestions: {profileSuggestions().jobTitles.join(', ')}
              </p>
            {/if}
          </div>
          <div class="grid gap-2">
            <label for="location" class="text-sm font-medium">Location</label>
            <Input
              id="location"
              bind:value={selectedLocation}
              placeholder="e.g. New York, Remote" />
            {#if profileSuggestions() && profileSuggestions().location}
              <p class="text-xs text-gray-500">
                From profile: {profileSuggestions().location}
              </p>
            {/if}
          </div>
        </div>

        <!-- Automation Settings -->
        {#if selectedProfileId && isProfileEligible}
          <div class="border-t pt-6">
            <h3 class="mb-4 text-lg font-medium">Automation Settings</h3>

            <!-- Execution Settings -->
            <div class="mb-6 grid gap-4">
              <div class="grid gap-2">
                <Label>Maximum Jobs to Apply: {maxJobsToApply}</Label>
                <input
                  type="range"
                  bind:value={maxJobsToApply}
                  min="1"
                  max="50"
                  step="1"
                  class="w-full" />
                <p class="text-sm text-gray-500">
                  Limit the number of jobs to automatically apply to in this run.
                </p>
              </div>

              <div class="grid gap-2">
                <Label>Minimum Match Score: {minMatchScore}%</Label>
                <input
                  type="range"
                  bind:value={minMatchScore}
                  min="50"
                  max="95"
                  step="5"
                  class="w-full" />
                <p class="text-sm text-gray-500">
                  Only apply to jobs with a match score above this threshold.
                </p>
              </div>

              <div class="flex items-center space-x-2">
                <input type="checkbox" id="auto-apply" bind:checked={autoApplyEnabled} />
                <Label for="auto-apply">Enable automatic job applications</Label>
              </div>
              <p class="text-sm text-gray-500">
                When enabled, the system will automatically submit applications. When disabled, it
                will only collect and score jobs for manual review.
              </p>
            </div>

            <!-- Salary and Experience Filters -->
            <div class="mb-4 grid grid-cols-2 gap-4">
              <div class="grid gap-2">
                <Label for="salary-min">Minimum Salary</Label>
                <select
                  id="salary-min"
                  bind:value={salaryMin}
                  class="border-input bg-background w-full rounded-md border px-3 py-2">
                  <option value="">No minimum</option>
                  {#each salaryOptions as option}
                    <option value={option.value}>{option.label}</option>
                  {/each}
                </select>
              </div>
              <div class="grid gap-2">
                <Label for="salary-max">Maximum Salary</Label>
                <select
                  id="salary-max"
                  bind:value={salaryMax}
                  class="border-input bg-background w-full rounded-md border px-3 py-2">
                  <option value="">No maximum</option>
                  {#each salaryOptions as option}
                    <option value={option.value}>{option.label}</option>
                  {/each}
                </select>
              </div>
            </div>

            <div class="mb-4 grid grid-cols-2 gap-4">
              <div class="grid gap-2">
                <Label for="exp-min">Minimum Experience</Label>
                <select
                  id="exp-min"
                  bind:value={experienceLevelMin}
                  class="border-input bg-background w-full rounded-md border px-3 py-2">
                  <option value="">No minimum</option>
                  {#each experienceOptions as option}
                    <option value={option.value}>{option.label}</option>
                  {/each}
                </select>
              </div>
              <div class="grid gap-2">
                <Label for="exp-max">Maximum Experience</Label>
                <select
                  id="exp-max"
                  bind:value={experienceLevelMax}
                  class="border-input bg-background w-full rounded-md border px-3 py-2">
                  <option value="">No maximum</option>
                  {#each experienceOptions as option}
                    <option value={option.value}>{option.label}</option>
                  {/each}
                </select>
              </div>
            </div>

            <!-- Remote Preference -->
            <div class="mb-4 grid gap-2">
              <Label>Remote Work Preference</Label>
              <select
                bind:value={remotePreference}
                class="border-input bg-background w-full rounded-md border px-3 py-2">
                <option value="any">Any</option>
                <option value="remote">Remote Only</option>
                <option value="hybrid">Hybrid</option>
                <option value="onsite">On-site Only</option>
              </select>
            </div>
          </div>
        {/if}
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={() => (createDialogOpen = false)}>Cancel</Button>
        <Button
          variant="default"
          onclick={createAutomationRun}
          disabled={!isFormValid || isCreatingRun}>
          {#if isCreatingRun}
            Creating...
          {:else}
            Start Automation
          {/if}
        </Button>
      </Dialog.Footer>
    </FeatureGuard>
  </Dialog.Content>
</Dialog.Root>

<!-- Automation Run Sheet -->
{#if selectedRun}
  <AutomationRunSheet
    bind:open={isSheetOpen}
    automationRun={selectedRun}
    onClose={() => {
      selectedRun = null;
    }}
    onRefresh={handleRunRefresh}
    onStop={() => {
      automationRuns.update((runs) =>
        runs.map((run: any) => {
          if (run.id === selectedRun.id) {
            return { ...run, status: 'stopped' };
          }
          return run;
        })
      );
    }} />
{/if}
