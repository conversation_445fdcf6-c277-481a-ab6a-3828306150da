<script lang="ts">
  import { superForm } from 'sveltekit-superforms';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import { toast } from 'svelte-sonner';
  import { User, Eye, Settings, Cookie, Briefcase, FileText } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';

  // Import components
  import Personal from './components/personal.svelte';
  import Privacy from './components/privacy.svelte';
  import Accessibility from './components/accessibility.svelte';
  import CookiePreferences from './components/cookie-preferences.svelte';
  import ApplicationPreferences from './components/application-preferences.svelte';
  import JobSearchPreferences from './components/job-search-preferences.svelte';
  import ResumePreferences from './components/resume-preferences.svelte';

  const { data } = $props();

  // Import invalidate for refreshing data and userProfileStore for reactivity
  import { invalidate } from '$app/navigation';
  import userProfileStore from '$lib/stores/user-profile';

  // Define tabs array for easier management
  const tabs = [
    { id: 'personal', label: 'Personal', icon: User, component: Personal },
    {
      id: 'applications',
      label: 'Applications',
      icon: Briefcase,
      component: ApplicationPreferences,
    },
    { id: 'job-search', label: 'Job Search', icon: Briefcase, component: JobSearchPreferences },
    { id: 'resume', label: 'Resume', icon: FileText, component: ResumePreferences },
    { id: 'privacy', label: 'Privacy', icon: Eye, component: Privacy },
    { id: 'accessibility', label: 'Accessibility', icon: Settings, component: Accessibility },
    { id: 'cookies', label: 'Cookies', icon: Cookie, component: CookiePreferences },
  ];

  // Active tab state
  let activeTab = $state('personal');

  // Initialize form with validation
  const form = superForm(data.form, {
    dataType: 'json',
    validationMethod: 'auto',
    taintedMessage: false, // Disable the browser's "unsaved changes" warning
    resetForm: false, // Don't reset the form after submission
    applyAction: true, // Apply the result from the server action
    // Don't invalidate all fields on error
    // Don't clear the form on submit
    onUpdated: ({ form }) => {
      console.log('Form updated:', form.data);
      if (form.valid) {
        toast.success('Account settings updated successfully');
      }
    },
    onError: ({ result }) => {
      console.error('Form error:', result);
      toast.error(result?.error?.message || 'Failed to update account settings');
    },
  });

  const { form: formData, enhance, submitting, delayed } = form;

  // Auto-save functionality with debounce
  let autoSaveTimeout: NodeJS.Timeout;
  let autoSaving = $state(false);

  function handleFormChange() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
      if (!$submitting && !autoSaving) {
        autoSaving = true;
        const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
        submitButton?.click();
        setTimeout(() => {
          autoSaving = false;
        }, 1000);
      }
    }, 500); // 0.5 second debounce for faster response
  }
</script>

<SEO
  title="Account Settings - Hirli"
  description="Manage your account settings, personal information, application preferences, job search settings, resume preferences, privacy, and accessibility options."
  keywords="account settings, user profile, application preferences, job search, resume settings, privacy settings, accessibility"
  url="https://hirli.com/dashboard/settings/account" />

<div class="border-border flex flex-col justify-between border-b p-6">
  <div class="flex items-center justify-between">
    <div class="flex flex-col">
      <h2 class="text-lg font-semibold">Account Settings</h2>
      <p class="text-muted-foreground text-sm">
        Manage your personal information, application preferences, job search settings, and privacy
        options.
      </p>
    </div>
    <div class="flex items-center gap-3">
      <div class="flex items-center gap-2">
        <div
          class="h-2 w-2 rounded-full {$delayed
            ? 'animate-pulse bg-orange-500'
            : $submitting
              ? 'bg-orange-500'
              : 'bg-green-500'}">
        </div>
        <span class="text-muted-foreground text-xs">
          {#if $delayed}
            Auto-saving...
          {:else if $submitting}
            Saving...
          {:else}
            Changes saved automatically
          {/if}
        </span>
      </div>
    </div>
  </div>
</div>
<Tabs.Root value={activeTab} onValueChange={(value) => (activeTab = value)}>
  <div class="border-border border-b p-0">
    <Tabs.List class="flex flex-row gap-2 divide-x">
      {#each tabs as tab}
        <Tabs.Trigger value={tab.id} class="flex-1 gap-2 rounded-none border-none">
          <div class="flex items-center gap-2">
            <tab.icon class="h-4 w-4" />
            <span>{tab.label}</span>
          </div>
        </Tabs.Trigger>
      {/each}
    </Tabs.List>
  </div>

  <div class="grid grid-cols-1 gap-6 md:grid-cols-[1fr_300px]">
    <form
      method="POST"
      use:enhance={{
        onSubmit: () => {
          console.log('Form submitted');
          // Show submitting state
          return async ({ result, update }) => {
            console.log('Form submission result:', result);
            if (result.type === 'success') {
              console.log('Form submission successful, updating form');

              // Update the user profile store and invalidate to refresh UI
              if (result.data?.user) {
                console.log('Updating user profile store with:', result.data.user);
                userProfileStore.set({
                  image: result.data.user.image || null,
                  name: result.data.user.name || null,
                  id: result.data.user.id || null,
                  email: result.data.user.email || null,
                  role: result.data.user.role || null,
                });

                // Also invalidate to refresh server data
                console.log('Invalidating user data to refresh UI');
                await invalidate('app:user');
              }

              // Update the form with the new data
              await update();
              console.log('Form updated');
            }
          };
        },
      }}
      onchange={handleFormChange}
      class="space-y-8">
      {#each tabs as tab}
        <Tabs.Content value={tab.id} class="mt-6">
          {#if tab.id === 'personal'}
            <Personal {form} {formData} />
          {:else if tab.id === 'applications'}
            <ApplicationPreferences {form} {formData} />
          {:else if tab.id === 'job-search'}
            <JobSearchPreferences {form} {formData} />
          {:else if tab.id === 'resume'}
            <ResumePreferences {form} {formData} />
          {:else if tab.id === 'privacy'}
            <Privacy {form} {formData} />
          {:else if tab.id === 'accessibility'}
            <Accessibility {form} {formData} />
          {:else if tab.id === 'cookies'}
            <CookiePreferences {form} {formData} />
          {/if}
        </Tabs.Content>
      {/each}

      <!-- Hidden submit button for auto-save functionality -->
      <button id="submit-button" type="submit" class="hidden" aria-label="Save settings"></button>
    </form>
  </div>
</Tabs.Root>
