import { PrismaClient } from "@prisma/client";
import {
  <PERSON>Type,
  JobStatus,
  processJobs,
  publishJob<PERSON>tatus,
  addJob,
} from "../utils/redis-jobs.js";
import { redis } from "../redis.js";

// Define a new job type for automation
const AUTOMATION_JOB_TYPE = "automation-job-search";

// Create a Prisma client for database operations
const prisma = new PrismaClient();

// Define a type for the extended PrismaClient with optional models
type ExtendedPrismaClient = PrismaClient & {
  automationRun?: any;
  profile?: any;
  jobListing?: any;
  resume?: any;
};

// Cast the prisma client to the extended type to avoid TypeScript errors
const extendedPrisma = prisma as ExtendedPrismaClient;

// Track if we've already subscribed to Redis channels
let redisSubscribed = false;

// Subscribe to automation job search channel
async function subscribeToAutomationChannel() {
  // Skip if already subscribed
  if (redisSubscribed) {
    console.log("[automation] Already subscribed to Redis channels, skipping");
    return;
  }

  try {
    // Create a duplicate Redis client for pub/sub without connecting it
    // This is necessary because a Redis client in subscriber mode cannot be used for other commands
    const subscriber = redis.duplicate();

    // Log the subscriber status
    console.log(
      `[automation] Subscriber Redis client status: ${subscriber.status}`
    );

    // For ioredis, we don't need to explicitly connect a duplicated client
    // It inherits the connection from the parent client
    // Just proceed with subscription without calling connect()

    // Subscribe to the automation job search channel
    console.log("[automation] Subscribing to job-search channel...");
    await subscriber.subscribe(
      "automation:job-search",
      async (message: string) => {
        try {
          // Check if message is valid
          if (!message) {
            console.log(
              "[automation] Received empty job search message, ignoring"
            );
            return;
          }

          // Parse the message data
          const data = JSON.parse(message);
          console.log("[automation] Received job search request:", data);

          // Validate required fields
          if (!data || !data.runId) {
            console.log("[automation] Invalid job search data, missing runId");
            return;
          }

          // Create a worker process record
          const jobId = await addJob(JobType.SEARCH, {
            ...data,
            type: AUTOMATION_JOB_TYPE,
          });

          // Try to update the automation run status if the model exists
          try {
            if (extendedPrisma.automationRun) {
              await extendedPrisma.automationRun.update({
                where: { id: data.runId },
                data: {
                  status: "running",
                  startedAt: new Date(),
                },
              });
            } else {
              console.log(
                "[automation] automationRun model not available, skipping status update"
              );
            }
          } catch (dbError) {
            console.error(
              "[automation] Error updating automation run status:",
              dbError
            );
            // Continue processing even if database update fails
          }

          console.log(
            `[automation] Created job ${jobId} for automation run ${data.runId}`
          );
        } catch (error) {
          console.error("[automation] Error processing message:", error);
        }
      }
    );

    // Also subscribe to stop channel
    try {
      console.log("[automation] Subscribing to stop channel...");
      await subscriber.subscribe("automation:stop", async (message: string) => {
        try {
          // Check if message is valid
          if (!message) {
            console.log("[automation] Received empty stop message, ignoring");
            return;
          }

          // Parse the message data
          const data = JSON.parse(message);
          console.log("[automation] Received stop request:", data);

          // Validate required fields
          if (!data || !data.runId) {
            console.log("[automation] Invalid stop data, missing runId");
            return;
          }

          // Find any running jobs for this automation run
          try {
            const runningJobs = await extendedPrisma.workerProcess.findMany({
              where: {
                data: {
                  path: ["runId"],
                  equals: data.runId,
                },
                status: JobStatus.PROCESSING,
              },
            });

            // Mark jobs as failed
            for (const job of runningJobs) {
              await extendedPrisma.workerProcess.update({
                where: { id: job.id },
                data: {
                  status: JobStatus.FAILED,
                  error: "Stopped by user",
                },
              });

              // Publish status update
              await publishJobStatus(JobType.SEARCH, {
                jobId: job.id,
                status: JobStatus.FAILED,
                message: "Job stopped by user",
              });
            }

            console.log(
              `[automation] Marked ${runningJobs.length} jobs as stopped`
            );
          } catch (dbError) {
            console.error(
              "[automation] Error updating worker processes:",
              dbError
            );
            // Continue processing even if database update fails
          }

          // Try to update the automation run status if the model exists
          try {
            if (extendedPrisma.automationRun) {
              await extendedPrisma.automationRun.update({
                where: { id: data.runId },
                data: {
                  status: "stopped",
                  stoppedAt: new Date(),
                },
              });
              console.log(`[automation] Stopped automation run ${data.runId}`);
            } else {
              console.log(
                "[automation] automationRun model not available, skipping status update"
              );
            }
          } catch (dbError) {
            console.error(
              "[automation] Error updating automation run status:",
              dbError
            );
            // Continue processing even if database update fails
          }
        } catch (error) {
          console.error("[automation] Error processing stop message:", error);
        }
      });

      console.log("[automation] Subscribed to automation channels");

      // Mark as subscribed so we don't try again
      redisSubscribed = true;
    } catch (error) {
      console.error("[automation] Error subscribing to stop channel:", error);
      throw error; // Re-throw to be caught by the handleAutomationJob function
    }
  } catch (error) {
    console.error("[automation] Error setting up Redis subscriptions:", error);
    throw error; // Re-throw to be caught by the handleAutomationJob function
  }
}

// Handle automation search jobs with enhanced matching
async function handleAutomationJob(job: any) {
  const {
    runId,
    profileId,
    keywords,
    location,
    resumeId,
    jobId,
    maxJobsToApply = 10,
    minMatchScore = 70,
    autoApplyEnabled = false,
    salaryMin,
    salaryMax,
    experienceLevelMin,
    experienceLevelMax,
    jobTypes = [],
    remotePreference = "any",
    companySizePreference = [],
    excludeCompanies = [],
    preferredCompanies = [],
    specifications = {},
  } = job;
  console.log(`[worker] Processing enhanced automation job for run: ${runId}`);

  // Subscribe to Redis channels if needed for this job
  try {
    console.log(
      "[automation] Subscribing to Redis channels for job processing..."
    );
    await subscribeToAutomationChannel();
    console.log("[automation] Successfully subscribed to Redis channels");
  } catch (subscribeError) {
    console.error(
      "[automation] Failed to subscribe to Redis channels:",
      subscribeError
    );
    console.log(
      "[automation] Continuing with job processing despite subscription failure"
    );
  }

  try {
    // Try to update the automation run status if the model exists
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            status: "running",
            progress: 10,
          },
        });
      } else {
        console.log(
          "[automation] automationRun model not available, skipping status update"
        );
      }
    } catch (dbError) {
      console.error(
        "[automation] Error updating automation run status:",
        dbError
      );
      // Continue processing even if database update fails
    }

    // Mock profile data if we can't access the database model
    let profile = null;
    try {
      if (extendedPrisma.profile) {
        profile = await extendedPrisma.profile.findUnique({
          where: { id: profileId },
          include: {
            data: true,
            resumes: {
              include: {
                document: true,
              },
            },
          },
        });
      } else {
        console.log(
          "[automation] profile model not available, using mock data"
        );
        // Create mock profile data
        profile = {
          id: profileId,
          data: {
            title: "Software Engineer",
            skills: ["JavaScript", "TypeScript", "React", "Node.js"],
          },
        };
      }
    } catch (dbError) {
      console.error("[automation] Error fetching profile data:", dbError);
      // Create mock profile data as fallback
      profile = {
        id: profileId,
        data: {
          title: "Software Engineer",
          skills: ["JavaScript", "TypeScript", "React", "Node.js"],
        },
      };
    }

    // Get resume data if available (or use mock data)
    let resume = null;
    if (resumeId) {
      try {
        if (extendedPrisma.resume) {
          resume = await extendedPrisma.resume.findUnique({
            where: { id: resumeId },
            // Skip the document include to avoid errors
          });
        }
      } catch (dbError) {
        console.error("[automation] Error fetching resume data:", dbError);
      }
    }

    // Try to update progress
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            progress: 20,
          },
        });
      }
    } catch (dbError) {
      console.error("[automation] Error updating progress:", dbError);
    }

    // Perform enhanced job search with matching
    const searchResults = await performEnhancedJobSearch(
      keywords || "",
      location || "",
      profile,
      resume,
      {
        maxJobsToApply,
        minMatchScore,
        autoApplyEnabled,
        salaryMin,
        salaryMax,
        experienceLevelMin,
        experienceLevelMax,
        jobTypes,
        remotePreference,
        companySizePreference,
        excludeCompanies,
        preferredCompanies,
        specifications,
      }
    );

    // Try to update progress
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            progress: 80,
          },
        });
      }
    } catch (dbError) {
      console.error("[automation] Error updating progress:", dbError);
    }

    // Store job listings in the database if the model exists
    let jobListings = [];
    try {
      if (extendedPrisma.jobListing) {
        jobListings = await Promise.all(
          searchResults.map(async (result) => {
            return await extendedPrisma.jobListing.create({
              data: {
                automationRunId: runId,
                title: result.title,
                company: result.company,
                location: result.location,
                description: result.description,
                salary: result.salary,
                jobType: result.jobType,
                applyUrl: result.applyUrl,
                sourceUrl: result.sourceUrl,
                postedAt: result.postedAt,
                isActive: true,
              },
            });
          })
        );
      } else {
        console.log(
          "[automation] jobListing model not available, skipping database storage"
        );
        // Create mock job listings with IDs
        jobListings = searchResults.map((result, index) => ({
          id: `mock-job-${index}`,
          ...result,
        }));
      }
    } catch (dbError) {
      console.error("[automation] Error storing job listings:", dbError);
      // Create mock job listings with IDs as fallback
      jobListings = searchResults.map((result, index) => ({
        id: `mock-job-${index}`,
        ...result,
      }));
    }

    // Try to update the automation run status
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            status: "completed",
            progress: 100,
            completedAt: new Date(),
          },
        });
      }
    } catch (dbError) {
      console.error(
        "[automation] Error updating automation run status:",
        dbError
      );
    }

    // Store results in the WorkerProcess record
    try {
      await extendedPrisma.workerProcess.update({
        where: { id: jobId },
        data: {
          data: {
            ...job,
            results: jobListings.map((jobItem: any) => jobItem.id),
          },
          status: JobStatus.COMPLETED,
          completedAt: new Date(),
        },
      });
    } catch (dbError) {
      console.error("[automation] Error updating worker process:", dbError);
    }

    // Publish status update
    await publishJobStatus(JobType.SEARCH, {
      jobId,
      status: JobStatus.COMPLETED,
      message: `Job search for automation run ${runId} completed.`,
      resultCount: jobListings.length,
    });

    console.log(`[worker] Completed automation job for run: ${runId}`);
  } catch (err) {
    console.error(`[worker] Failed to process automation job:`, err);

    // Try to update the automation run status
    try {
      if (extendedPrisma.automationRun) {
        await extendedPrisma.automationRun.update({
          where: { id: runId },
          data: {
            status: "failed",
            error: err instanceof Error ? err.message : String(err),
            failedAt: new Date(),
          },
        });
      }
    } catch (dbError) {
      console.error(
        "[automation] Error updating automation run status:",
        dbError
      );
    }

    // Publish failure status
    await publishJobStatus(JobType.SEARCH, {
      jobId,
      status: JobStatus.FAILED,
      message: `Job search for automation run ${runId} failed.`,
      error: err instanceof Error ? err.message : String(err),
    });

    // Re-throw to let the processJobs handler update the job status
    throw err;
  }
}

// Perform job search based on profile and resume data
async function performJobSearch(
  keywords: string,
  location: string,
  profile: any,
  resume: any // Not used yet, but kept for future implementation
) {
  console.log(
    `[search] Searching for jobs with keywords: "${keywords}" in location: "${location}"`
  );
  console.log(`[search] Using profile: ${profile.id}`);

  // Extract skills from profile data
  const skills = profile.data?.skills || [];
  console.log(`[search] Skills: ${skills.join(", ")}`);

  // Extract job title from profile data
  const jobTitle = profile.data?.title || "";
  console.log(`[search] Job title: ${jobTitle}`);

  // Combine keywords with profile data for better search
  const searchQuery = [keywords, jobTitle, ...skills.slice(0, 5)]
    .filter(Boolean)
    .join(" ");
  console.log(`[search] Combined search query: "${searchQuery}"`);

  // Simulate search delay
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // Generate mock results
  const results = [];
  const numResults = Math.floor(Math.random() * 10) + 5; // 5-15 results

  for (let i = 0; i < numResults; i++) {
    const postedDays = Math.floor(Math.random() * 30);
    const postedDate = new Date();
    postedDate.setDate(postedDate.getDate() - postedDays);

    results.push({
      title: getRandomJobTitle(jobTitle, keywords),
      company: getRandomCompany(),
      location: location || getRandomLocation(),
      description: getRandomDescription(skills),
      salary: getRandomSalary(),
      jobType: getRandomJobType(),
      applyUrl: `https://example.com/jobs/${i}`,
      sourceUrl: `https://example.com/jobs/${i}`,
      postedAt: postedDate,
    });
  }

  return results;
}

// Enhanced job search with intelligent matching and filtering
async function performEnhancedJobSearch(
  keywords: string,
  location: string,
  profile: any,
  resume: any,
  automationConfig: any
) {
  console.log(
    `[enhanced-search] Starting enhanced job search for profile: ${profile.id}`
  );
  console.log(`[enhanced-search] Automation config:`, automationConfig);

  // Extract profile data for matching
  const profileData = profile.data?.data || profile.data || {};
  const skills = extractSkills(profileData);
  const experienceYears = calculateExperienceYears(profileData);
  const jobTitles = extractJobTitles(profileData);

  console.log(`[enhanced-search] Profile skills: ${skills.join(", ")}`);
  console.log(`[enhanced-search] Experience years: ${experienceYears}`);
  console.log(`[enhanced-search] Job titles: ${jobTitles.join(", ")}`);

  // Create job match criteria
  const matchCriteria = {
    profileId: profile.id,
    skills,
    experienceYears,
    jobTitles,
    salaryMin: automationConfig.salaryMin,
    salaryMax: automationConfig.salaryMax,
    remotePreference: automationConfig.remotePreference,
    companySizePreference: automationConfig.companySizePreference,
    excludeCompanies: automationConfig.excludeCompanies,
    preferredCompanies: automationConfig.preferredCompanies,
  };

  // Generate enhanced mock jobs with better matching
  const allJobs = await generateEnhancedMockJobs(
    keywords,
    location,
    matchCriteria
  );
  console.log(`[enhanced-search] Generated ${allJobs.length} potential jobs`);

  // Filter and score jobs
  const scoredJobs = allJobs
    .map((job) => {
      const matchResult = calculateJobMatchScore(job, matchCriteria);
      return {
        ...job,
        matchScore: matchResult.overallScore,
        matchReasons: matchResult.reasons,
        skillsMatch: matchResult.skillsMatch,
        experienceMatch: matchResult.experienceMatch,
      };
    })
    .filter((job) => {
      // Apply filters
      if (job.matchScore < automationConfig.minMatchScore) return false;
      if (automationConfig.excludeCompanies.includes(job.company)) return false;
      if (
        automationConfig.jobTypes.length > 0 &&
        !automationConfig.jobTypes.includes(job.jobType?.toLowerCase())
      )
        return false;

      return true;
    })
    .sort((a, b) => b.matchScore - a.matchScore) // Sort by match score
    .slice(0, automationConfig.maxJobsToApply); // Limit results

  console.log(
    `[enhanced-search] Filtered to ${scoredJobs.length} matching jobs`
  );
  console.log(
    `[enhanced-search] Average match score: ${scoredJobs.reduce((sum, job) => sum + job.matchScore, 0) / scoredJobs.length || 0}%`
  );

  // If auto-apply is enabled, simulate applications
  if (automationConfig.autoApplyEnabled) {
    console.log(
      `[enhanced-search] Auto-apply enabled, simulating applications...`
    );
    for (const job of scoredJobs) {
      await simulateJobApplication(job, profile, automationConfig);
    }
  }

  return scoredJobs;
}

// Extract skills from profile data
function extractSkills(profileData: any): string[] {
  const skills = [];

  // Try different skill data structures
  if (profileData.skillsData?.list) {
    skills.push(...profileData.skillsData.list);
  } else if (Array.isArray(profileData.skills)) {
    skills.push(...profileData.skills);
  } else if (profileData.skills) {
    skills.push(profileData.skills);
  }

  // Extract skills from work experience
  if (profileData.workExperience) {
    profileData.workExperience.forEach((exp: any) => {
      if (exp.skills) {
        skills.push(...exp.skills);
      }
    });
  }

  return [...new Set(skills)].filter(Boolean); // Remove duplicates and empty values
}

// Calculate total years of experience
function calculateExperienceYears(profileData: any): number {
  if (!profileData.workExperience) return 0;

  let totalYears = 0;
  profileData.workExperience.forEach((exp: any) => {
    if (exp.startDate) {
      const start = new Date(exp.startDate);
      const end = exp.current
        ? new Date()
        : new Date(exp.endDate || new Date());
      const years =
        (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365);
      totalYears += Math.max(0, years);
    }
  });

  return Math.floor(totalYears);
}

// Extract job titles from work experience
function extractJobTitles(profileData: any): string[] {
  const titles = [];

  if (profileData.personalInfo?.jobTitle) {
    titles.push(profileData.personalInfo.jobTitle);
  }

  if (profileData.workExperience) {
    profileData.workExperience.forEach((exp: any) => {
      if (exp.title) {
        titles.push(exp.title);
      }
    });
  }

  return [...new Set(titles)].filter(Boolean);
}

// Calculate job match score (simplified version of the web utility)
function calculateJobMatchScore(job: any, criteria: any) {
  let overallScore = 0;
  const reasons: string[] = [];

  // Skills matching (40% weight)
  const jobSkills = job.requiredSkills || [];
  const matchedSkills = criteria.skills.filter((skill: string) =>
    jobSkills.some(
      (jobSkill: string) =>
        jobSkill.toLowerCase().includes(skill.toLowerCase()) ||
        skill.toLowerCase().includes(jobSkill.toLowerCase())
    )
  );
  const skillsScore =
    jobSkills.length > 0 ? (matchedSkills.length / jobSkills.length) * 100 : 50;
  overallScore += skillsScore * 0.4;
  if (matchedSkills.length > 0) {
    reasons.push(`Matched ${matchedSkills.length} skills`);
  }

  // Experience matching (25% weight)
  const requiredYears = job.experienceYears || 0;
  const experienceMatch = criteria.experienceYears >= requiredYears - 1;
  const experienceScore = experienceMatch
    ? 100
    : Math.max(0, 100 - (requiredYears - criteria.experienceYears) * 20);
  overallScore += experienceScore * 0.25;
  if (experienceMatch) {
    reasons.push("Experience level matches");
  }

  // Title matching (20% weight)
  const titleMatch = criteria.jobTitles.some(
    (title: string) =>
      job.title.toLowerCase().includes(title.toLowerCase()) ||
      title.toLowerCase().includes(job.title.toLowerCase())
  );
  const titleScore = titleMatch ? 100 : 30;
  overallScore += titleScore * 0.2;
  if (titleMatch) {
    reasons.push("Job title matches profile");
  }

  // Salary matching (10% weight)
  let salaryScore = 100;
  if (
    criteria.salaryMin &&
    job.salaryMax &&
    job.salaryMax < criteria.salaryMin
  ) {
    salaryScore = 0;
  }
  if (
    criteria.salaryMax &&
    job.salaryMin &&
    job.salaryMin > criteria.salaryMax
  ) {
    salaryScore = 0;
  }
  overallScore += salaryScore * 0.1;
  if (salaryScore > 0) {
    reasons.push("Salary meets requirements");
  }

  // Location/Remote matching (5% weight)
  let locationScore = 100;
  if (criteria.remotePreference === "remote" && !job.isRemote) {
    locationScore = 20;
  }
  overallScore += locationScore * 0.05;

  // Company preference bonus
  if (criteria.preferredCompanies.includes(job.company)) {
    overallScore += 10;
    reasons.push("Preferred company");
  }

  return {
    overallScore: Math.min(100, Math.max(0, overallScore)),
    reasons,
    skillsMatch: { matchedSkills, skillsScore },
    experienceMatch: { isMatch: experienceMatch, experienceScore },
  };
}

// Generate enhanced mock jobs with better matching potential
async function generateEnhancedMockJobs(
  keywords: string,
  location: string,
  criteria: any
) {
  const jobs = [];
  const numJobs = Math.floor(Math.random() * 20) + 30; // 30-50 jobs

  for (let i = 0; i < numJobs; i++) {
    const job = {
      id: `enhanced-job-${i}`,
      title: generateRelevantJobTitle(criteria.jobTitles, keywords),
      company: generateRelevantCompany(
        criteria.preferredCompanies,
        criteria.excludeCompanies
      ),
      location: location || getRandomLocation(),
      description: generateRelevantDescription(criteria.skills),
      salary: generateRelevantSalary(criteria.salaryMin, criteria.salaryMax),
      salaryMin: generateSalaryNumber(
        criteria.salaryMin,
        criteria.salaryMax,
        "min"
      ),
      salaryMax: generateSalaryNumber(
        criteria.salaryMin,
        criteria.salaryMax,
        "max"
      ),
      jobType: generateRelevantJobType(),
      experienceYears: Math.floor(Math.random() * 10),
      requiredSkills: generateRelevantSkills(criteria.skills),
      isRemote: Math.random() > 0.6, // 40% remote jobs
      applyUrl: `https://example.com/jobs/${i}`,
      sourceUrl: `https://example.com/jobs/${i}`,
      postedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Last 30 days
    };
    jobs.push(job);
  }

  return jobs;
}

// Helper functions for enhanced job generation
function generateRelevantJobTitle(
  profileTitles: string[],
  keywords: string
): string {
  const relevantTitles = [
    ...profileTitles,
    keywords,
    `Senior ${profileTitles[0] || keywords}`,
    `Lead ${profileTitles[0] || keywords}`,
    `${profileTitles[0] || keywords} Specialist`,
  ].filter(Boolean);

  const genericTitles = [
    "Software Engineer",
    "Full Stack Developer",
    "Frontend Developer",
    "Backend Developer",
    "DevOps Engineer",
    "Data Scientist",
  ];

  const allTitles = [...relevantTitles, ...genericTitles];
  return allTitles[Math.floor(Math.random() * allTitles.length)];
}

function generateRelevantCompany(
  preferred: string[],
  excluded: string[]
): string {
  const companies = [
    "TechCorp",
    "Innovate Solutions",
    "Digital Dynamics",
    "Future Systems",
    "Global Tech",
    "NextGen Software",
    "Quantum Computing",
    "Cyber Security Inc.",
  ];

  // Add preferred companies with higher probability
  const weightedCompanies = [...companies, ...preferred, ...preferred];

  // Remove excluded companies
  const availableCompanies = weightedCompanies.filter(
    (company) => !excluded.includes(company)
  );

  return (
    availableCompanies[Math.floor(Math.random() * availableCompanies.length)] ||
    companies[0]
  );
}

function generateRelevantSkills(profileSkills: string[]): string[] {
  const commonSkills = [
    "JavaScript",
    "Python",
    "React",
    "Node.js",
    "SQL",
    "Git",
  ];
  const relevantSkills = [...profileSkills.slice(0, 3), ...commonSkills];

  // Return 2-5 random skills
  const numSkills = Math.floor(Math.random() * 4) + 2;
  return relevantSkills.slice(0, numSkills);
}

function generateRelevantSalary(
  minSalary?: number,
  maxSalary?: number
): string {
  if (minSalary || maxSalary) {
    const min = minSalary || 50000;
    const max = maxSalary || 200000;
    return `$${min.toLocaleString()} - $${max.toLocaleString()}`;
  }

  const salaries = [
    "$80,000 - $100,000",
    "$100,000 - $120,000",
    "$120,000 - $150,000",
    "$150,000 - $180,000",
    "Competitive",
    "Based on experience",
  ];
  return salaries[Math.floor(Math.random() * salaries.length)];
}

function generateSalaryNumber(
  minSalary?: number,
  maxSalary?: number,
  type: "min" | "max" = "min"
): number | null {
  if (type === "min" && minSalary)
    return minSalary + Math.floor(Math.random() * 20000);
  if (type === "max" && maxSalary)
    return maxSalary - Math.floor(Math.random() * 20000);

  const baseSalary = 80000 + Math.floor(Math.random() * 120000);
  return type === "min" ? baseSalary : baseSalary + 20000;
}

function generateRelevantJobType(): string {
  const types = ["full-time", "part-time", "contract", "remote", "hybrid"];
  return types[Math.floor(Math.random() * types.length)];
}

// Simulate job application process
async function simulateJobApplication(job: any, profile: any, config: any) {
  console.log(
    `[auto-apply] Applying to: ${job.title} at ${job.company} (Match: ${job.matchScore}%)`
  );

  // Simulate application delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Simulate application success/failure
  const success = Math.random() > 0.1; // 90% success rate

  if (success) {
    console.log(
      `[auto-apply] ✅ Successfully applied to ${job.title} at ${job.company}`
    );
    job.applicationStatus = "applied";
    job.appliedAt = new Date();
  } else {
    console.log(
      `[auto-apply] ❌ Failed to apply to ${job.title} at ${job.company}`
    );
    job.applicationStatus = "failed";
  }
}

// Helper functions to generate mock data
function getRandomJobTitle(jobTitle: string, keywords: string) {
  const titles = [
    jobTitle,
    `Senior ${jobTitle}`,
    `${jobTitle} Specialist`,
    keywords,
    `${keywords} Expert`,
    "Software Engineer",
    "Full Stack Developer",
    "Frontend Developer",
    "Backend Developer",
    "DevOps Engineer",
    "Data Scientist",
    "Product Manager",
    "UX Designer",
  ];
  return titles[Math.floor(Math.random() * titles.length)];
}

function getRandomCompany() {
  const companies = [
    "Acme Inc.",
    "TechCorp",
    "Innovate Solutions",
    "Digital Dynamics",
    "Future Systems",
    "Global Tech",
    "NextGen Software",
    "Quantum Computing",
    "Cyber Security Inc.",
    "Data Analytics Co.",
  ];
  return companies[Math.floor(Math.random() * companies.length)];
}

function getRandomLocation() {
  const locations = [
    "New York, NY",
    "San Francisco, CA",
    "Austin, TX",
    "Seattle, WA",
    "Boston, MA",
    "Chicago, IL",
    "Los Angeles, CA",
    "Denver, CO",
    "Atlanta, GA",
    "Remote",
  ];
  return locations[Math.floor(Math.random() * locations.length)];
}

function getRandomDescription(skills: string[]) {
  const descriptions = [
    `We are looking for a talented professional with experience in ${skills
      .slice(0, 3)
      .join(", ")}.`,
    `Join our team and work on exciting projects using cutting-edge technologies like ${skills
      .slice(0, 3)
      .join(", ")}.`,
    `This role requires expertise in ${skills
      .slice(0, 3)
      .join(", ")} and a passion for innovation.`,
    `We need someone who can hit the ground running with skills in ${skills
      .slice(0, 3)
      .join(", ")}.`,
    `If you're proficient in ${skills
      .slice(0, 3)
      .join(", ")}, we want to talk to you!`,
  ];
  return descriptions[Math.floor(Math.random() * descriptions.length)];
}

function getRandomSalary() {
  const salaries = [
    "$80,000 - $100,000",
    "$100,000 - $120,000",
    "$120,000 - $150,000",
    "$150,000 - $180,000",
    "$180,000 - $200,000",
    "Competitive",
    "Based on experience",
    "Negotiable",
  ];
  return salaries[Math.floor(Math.random() * salaries.length)];
}

function getRandomJobType() {
  const jobTypes = [
    "Full-time",
    "Part-time",
    "Contract",
    "Freelance",
    "Remote",
    "Hybrid",
  ];
  return jobTypes[Math.floor(Math.random() * jobTypes.length)];
}

// Start the automation worker
async function startAutomationWorker() {
  try {
    console.log("[automation] Starting worker...");

    // Check if Redis is connected
    if (redis.status !== "ready") {
      console.log(
        `[automation] Redis status: ${redis.status}, waiting for connection...`
      );
      // Wait for Redis to connect using events instead of polling
      await new Promise<boolean>((resolve) => {
        // Set up a one-time event handler for when Redis is ready
        redis.once("ready", () => {
          resolve(true);
        });

        // Set a timeout to avoid waiting forever
        setTimeout(() => {
          resolve(false);
        }, 10000); // 10 second timeout
      });
    }

    console.log(`[automation] Redis status: ${redis.status}`);

    // Skip Redis subscription entirely - we'll only subscribe when needed
    console.log(
      "[automation] Skipping Redis subscription - will only subscribe when processing jobs"
    );

    // Process jobs from the search queue
    processJobs(JobType.SEARCH, handleAutomationJob).catch((err) => {
      console.error("[automation] Error processing jobs:", err);
    });

    console.log("[automation] Worker started successfully");
  } catch (error) {
    console.error("[automation] Failed to start worker:", error);

    // Continue with job processing even if subscription fails
    console.log(
      "[automation] Continuing with job processing despite startup failure"
    );
    processJobs(JobType.SEARCH, handleAutomationJob).catch((err) => {
      console.error("[automation] Error processing jobs:", err);
    });
  }
}

// Start the worker
startAutomationWorker().catch((err) => console.error(err));

// Handle process exit signals
process.on("SIGINT", async () => {
  console.log("Received SIGINT signal, shutting down automation worker...");
  await cleanup();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("Received SIGTERM signal, shutting down automation worker...");
  await cleanup();
  console.log(
    "Cleanup completed, but keeping service alive to allow workers to finish."
  );
  // Removed process.exit(0) to keep the process alive
});

// Cleanup function to close connections
async function cleanup() {
  console.log("Cleaning up resources...");
  try {
    // Close Redis connection
    if (redis && redis.disconnect) {
      await redis.disconnect();
    }

    // Close Prisma connection
    if (extendedPrisma && extendedPrisma.$disconnect) {
      await extendedPrisma.$disconnect();
    }

    console.log("Cleanup completed successfully");
  } catch (error) {
    console.error("Error during cleanup:", error);
  }
}
