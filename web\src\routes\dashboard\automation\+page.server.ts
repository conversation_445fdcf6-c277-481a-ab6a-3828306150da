// File: src/routes/dashboard/automation/+page.server.ts
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import { calculateProfileCompletion } from '$lib/utils/profileHelpers';
import type { PageServerLoad } from '../$types.js';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ locals }) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  locals.user = user;

  // Get profiles with associated resumes
  const profiles = await prisma.profile.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          team: {
            members: {
              some: { userId: user.id },
            },
          },
        },
      ],
    },
    include: {
      data: true,
      team: true,
      // Include documents of type resume associated with this profile
      documents: {
        where: {
          type: 'resume',
        },
      },
    },
  });

  // Get real automation runs from the database
  const automationRuns = await prisma.automationRun.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          profile: {
            team: {
              members: {
                some: { userId: user.id },
              },
            },
          },
        },
      ],
    },
    include: {
      profile: {
        include: {
          data: true,
          documents: {
            where: {
              type: 'resume',
            },
          },
        },
      },
      jobs: {
        orderBy: {
          createdAt: 'desc',
        },
        take: 5, // Limit to 5 jobs per run for the overview page
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  // Get all resumes for the user
  const documents = await prisma.document.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          profile: {
            team: {
              members: {
                some: { userId: user.id },
              },
            },
          },
        },
      ],
      type: 'resume',
    },
    select: {
      id: true,
      label: true,
      fileName: true,
      createdAt: true,
    },
  });

  // Format resumes for the dropdown
  const resumes = documents.map((doc) => ({
    id: doc.id,
    label: doc.label || doc.fileName || `Resume (${new Date(doc.createdAt).toLocaleDateString()})`,
  }));

  // Calculate profile completion percentages using shared utility (same as dashboard)
  const profilesWithCompletion = profiles.map((profile) => {
    let completionPercentage = 0;

    // Use shared utility if profile has data
    if (profile.data?.data) {
      // Parse profile data properly - it might be a JSON string
      let profileData = profile.data.data;
      if (typeof profileData === 'string') {
        try {
          profileData = JSON.parse(profileData);
        } catch (e) {
          console.error('Error parsing profile data JSON:', e);
          profileData = {};
        }
      }
      completionPercentage = calculateProfileCompletion(profileData as any);
    }

    return {
      ...profile,
      completionPercentage,
    };
  });

  return {
    user,
    profiles: profilesWithCompletion,
    automationRuns,
    resumes,
  };
};
