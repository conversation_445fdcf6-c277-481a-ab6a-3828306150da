<script lang="ts">
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import * as Scroll<PERSON><PERSON> from '$lib/components/ui/scroll-area';
  import { notifications, unreadCount, type Notification } from '$lib/stores/notification';
  import { Separator } from '$lib/components/ui/separator';
  import { Button } from '$lib/components/ui/button';
  import { browser } from '$app/environment';
  import { onDestroy } from 'svelte';
  import websocket from '$lib/websocket/websocket-singleton';
  import {
    Bell,
    X,
    Info,
    AlertTriangle,
    CheckCircle,
    Briefcase,
    MessageSquare,
  } from 'lucide-svelte';

  // Export the component to be used with default import
  export const NotificationDropdown = {};

  // State variables
  let notificationUnreadCount = 0;
  let wsStatus: 'connected' | 'disconnected' | 'connecting' | 'error' = 'disconnected';
  let unreadNotifications: Notification[] = [];

  // Get icon for notification type
  function getNotificationIcon(type: string | undefined) {
    switch (type) {
      case 'job':
        return Briefcase;
      case 'error':
        return AlertTriangle;
      case 'success':
        return CheckCircle;
      case 'message':
        return MessageSquare;
      case 'info':
      default:
        return Info;
    }
  }

  // Debug function to log WebSocket status changes
  function logWebSocketStatus(status: string) {
    console.log(`NotificationDropdown: WebSocket status changed to ${status}`);
    return status;
  }

  // Initialize component directly
  if (browser) {
    console.log('NotificationDropdown initialized');

    // Expose stores to window for debugging
    console.log('Exposing notification stores to window object for debugging');
    (window as any).notifications = notifications;
    (window as any).websocket = websocket;

    // Subscribe to notification data
    notifications.subscribe((value: Notification[]) => {
      unreadNotifications = value.filter((n: Notification) => !n.read);
      console.log('Notifications updated:', value.length);
    });

    // Subscribe to unread count
    unreadCount.subscribe((count: number) => {
      notificationUnreadCount = count;
      console.log(`Unread count updated: ${count}`);
    });

    // Get current WebSocket status directly
    wsStatus = logWebSocketStatus(websocket.getStatus()) as typeof wsStatus;

    // Subscribe to WebSocket status changes
    websocket.status.subscribe(
      (currentStatus: 'connected' | 'disconnected' | 'connecting' | 'error') => {
        // Only update if status actually changed
        if (wsStatus !== currentStatus) {
          wsStatus = logWebSocketStatus(currentStatus) as typeof wsStatus;
        }
      }
    );
  }
</script>

<DropdownMenu.Root>
  <DropdownMenu.Trigger class="relative cursor-pointer">
    <div class="relative">
      <Bell
        class={`h-5 w-5 ${notificationUnreadCount > 0 ? 'text-yellow-400' : 'text-gray-500 hover:text-gray-700'}`} />
      {#if notificationUnreadCount > 0}
        <div
          class="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full border border-white bg-red-500 text-[8px] font-bold text-white">
          {notificationUnreadCount > 9 ? '9+' : notificationUnreadCount}
        </div>
      {/if}
    </div>
  </DropdownMenu.Trigger>

  <DropdownMenu.Content class="m-0 w-80 rounded-none  p-0" sideOffset={22} align="end">
    {#if unreadNotifications?.length > 0}
      <div class="flex items-center justify-between border-b p-2">
        <div class="flex items-center gap-2">
          <h3 class="ml-2 text-sm font-medium">Notifications</h3>
        </div>
        <div class="flex gap-2">
          {#if unreadNotifications.length > 0}
            <Button
              variant="ghost"
              size="sm"
              class="h-8 text-xs"
              onclick={async () => {
                try {
                  await notifications.clearAll();
                  console.log('All notifications marked as read');
                } catch (error) {
                  console.error('Error clearing notifications:', error);
                }
              }}>
              Mark All Read
            </Button>
          {/if}
        </div>
      </div>
      <ScrollArea.Root class={unreadNotifications.length > 3 ? 'h-[250px]' : ''}>
        {#each unreadNotifications as notif, index (notif.id)}
          {#if notif.url}
            <a href={notif.url} target="_blank" rel="noopener noreferrer" class="no-underline">
              <DropdownMenu.Item
                class="hover:bg-muted group flex items-start justify-between p-3 {notif.read
                  ? 'opacity-70'
                  : ''}">
                <div class="flex gap-3">
                  <div class="mt-0.5 flex-shrink-0">
                    {#if notif.type}
                      {@const Icon = getNotificationIcon(notif.type)}
                      <Icon class="text-muted-foreground h-2 w-2" />
                    {:else}
                      <Info class="text-muted-foreground h-2 w-2" />
                    {/if}
                  </div>
                  <div class="flex flex-col items-start gap-1">
                    <p class="text-sm font-medium text-blue-600 group-hover:underline">
                      {notif.title}
                    </p>
                    <p class="text-muted-foreground text-xs">{notif.message}</p>
                    <span class="text-[10px] text-gray-400">
                      {notif.timestamp.toLocaleString()}
                    </span>
                  </div>
                </div>

                <X
                  class="h-2 w-2 cursor-pointer text-xs text-gray-400 opacity-0 transition-opacity hover:text-red-500 group-hover:opacity-100"
                  onclick={async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    try {
                      await notifications.removeNotification(notif.id);
                      console.log('Notification marked as read');
                    } catch (error) {
                      console.error('Error marking notification as read:', error);
                    }
                  }} />
              </DropdownMenu.Item>
            </a>
          {:else}
            <DropdownMenu.Item
              class="hover:bg-muted group flex items-start justify-between p-3 {notif.read
                ? 'opacity-70'
                : ''}">
              <div class="flex gap-3">
                <div class="mt-0.5 flex-shrink-0">
                  {#if notif.type}
                    {@const Icon = getNotificationIcon(notif.type)}
                    <Icon class="text-muted-foreground h-4 w-4" />
                  {:else}
                    <Info class="text-muted-foreground h-4 w-4" />
                  {/if}
                </div>
                <div class="flex flex-col items-start gap-1">
                  <p class="text-sm font-medium">{notif.title}</p>
                  <p class="text-muted-foreground text-xs">{notif.message}</p>
                  <span class="text-[10px] text-gray-400">
                    {notif.timestamp.toLocaleString()}
                  </span>
                </div>
              </div>

              <X
                class="h-2 w-2 cursor-pointer text-xs text-gray-400 opacity-0 transition-opacity hover:text-red-500 group-hover:opacity-100"
                onclick={async (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  try {
                    await notifications.removeNotification(notif.id);
                    console.log('Notification marked as read');
                  } catch (error) {
                    console.error('Error marking notification as read:', error);
                  }
                }} />
            </DropdownMenu.Item>
          {/if}

          {#if index < unreadNotifications.length - 1}
            <Separator class="border border-b" />
          {/if}
        {/each}
        <ScrollArea.Scrollbar orientation="vertical" />
      </ScrollArea.Root>
    {:else}
      <DropdownMenu.Item class="p-4">You are all caught up!</DropdownMenu.Item>
    {/if}
  </DropdownMenu.Content>
</DropdownMenu.Root>
